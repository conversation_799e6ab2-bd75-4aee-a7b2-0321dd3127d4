#!/usr/bin/env python3
"""
Layout Test Script
Test the GUI layout without camera dependencies
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

class LayoutTestApp:
    """Simplified app for testing layout only"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("QHYCCD Layout Test")
        self.root.geometry("1453x900")  # Updated to match main app
        self.root.resizable(True, True)
        
        # Create test frames
        self.create_connection_frame()
        self.create_register_frame()
        self.create_parameter_frame()
        self.create_image_frame()
        self.create_test_frame()
        self.create_serial_frame()
        self.create_status_frame()
        
        # Add frame borders for visibility
        self.add_debug_borders()
    
    def create_connection_frame(self):
        """Create camera connection controls"""
        frame = ttk.LabelFrame(self.root, text="Camera Connection", padding=10)
        frame.place(x=10, y=8, width=460, height=110)  # Updated height

        # ReadMode button (first row, left)
        btn_read_mode = ttk.Button(frame, text="ReadMode")
        btn_read_mode.place(x=0, y=0, width=70, height=28)

        # Read mode combo
        ttk.Label(frame, text="Read Mode:").place(x=80, y=2)
        combo_read_mode = ttk.Combobox(frame, width=15, state="readonly")
        combo_read_mode.place(x=150, y=0)
        combo_read_mode.set("Test Mode")

        # Total modes label
        label_total_modes = ttk.Label(frame, text="Total ReadModes: 5")
        label_total_modes.place(x=280, y=2)

        # Connect button (second row, right)
        btn_connect = ttk.Button(frame, text="连接相机")
        btn_connect.place(x=340, y=30, width=105, height=28)

        # COM port selection (second row, left)
        ttk.Label(frame, text="COM Port:").place(x=0, y=32)
        combo_com_port = ttk.Combobox(frame, width=10)
        combo_com_port.set("COM3")
        combo_com_port.place(x=80, y=30)

        # Serial connect button (second row, center)
        btn_serial_connect = ttk.Button(frame, text="打开串口连接")
        btn_serial_connect.place(x=200, y=30, width=120, height=25)

        # Close camera button (third row)
        btn_close_camera = ttk.Button(frame, text="关闭相机")
        btn_close_camera.place(x=0, y=60, width=80, height=25)
    
    def create_register_frame(self):
        """Create register controls"""
        frame = ttk.LabelFrame(self.root, text="Register Operations", padding=10)
        frame.place(x=8, y=125, width=460, height=250)  # Updated position
        
        # Add some test controls
        ttk.Label(frame, text="Register Address:").place(x=0, y=0)
        entry_reg_addr = ttk.Entry(frame, width=15)
        entry_reg_addr.insert(0, "0000")
        entry_reg_addr.place(x=0, y=20)
        
        ttk.Label(frame, text="Register Value:").place(x=120, y=0)
        entry_reg_value = ttk.Entry(frame, width=15)
        entry_reg_value.insert(0, "0012")
        entry_reg_value.place(x=120, y=20)
        
        ttk.Button(frame, text="WriteREG").place(x=240, y=20, width=80, height=30)
        ttk.Button(frame, text="ReadREG").place(x=330, y=20, width=80, height=30)
    
    def create_parameter_frame(self):
        """Create parameter controls"""
        frame = ttk.LabelFrame(self.root, text="Camera Parameters", padding=10)
        frame.place(x=8, y=383, width=460, height=400)  # Updated position
        
        # Add some test controls
        ttk.Label(frame, text="Gain:").place(x=0, y=0)
        scale_gain = ttk.Scale(frame, from_=0, to=511, orient=tk.HORIZONTAL)
        scale_gain.place(x=50, y=0, width=150, height=25)
        
        ttk.Label(frame, text="Exposure:").place(x=0, y=30)
        scale_exposure = ttk.Scale(frame, from_=1000, to=1000000, orient=tk.HORIZONTAL)
        scale_exposure.place(x=70, y=30, width=150, height=25)
    
    def create_image_frame(self):
        """Create image display area"""
        frame = ttk.LabelFrame(self.root, text="Image Display", padding=10)
        frame.place(x=478, y=8, width=720, height=420)
        
        # Preview canvas
        canvas_preview = tk.Canvas(frame, width=352, height=201, bg='black')
        canvas_preview.place(x=0, y=0)
        canvas_preview.create_text(176, 100, text="Preview", fill="white")
        
        # Flat canvas
        canvas_flat = tk.Canvas(frame, width=352, height=201, bg='gray20')
        canvas_flat.place(x=360, y=0)
        canvas_flat.create_text(176, 100, text="Flat", fill="white")
        
        # Control buttons
        ttk.Button(frame, text="SHOWIMG").place(x=10, y=220, width=80, height=30)
        ttk.Button(frame, text="STOPSHOW").place(x=100, y=220, width=80, height=30)
    
    def create_test_frame(self):
        """Create test controls"""
        frame = ttk.LabelFrame(self.root, text="Test Automation", padding=10)
        frame.place(x=1200, y=8, width=240, height=500)
        
        ttk.Button(frame, text="LiveFrame").place(x=0, y=0, width=100, height=40)
        ttk.Button(frame, text="Run Test").place(x=0, y=50, width=100, height=40)
        ttk.Button(frame, text="Stop Test").place(x=110, y=50, width=100, height=40)
    
    def create_serial_frame(self):
        """Create serial controls"""
        frame = ttk.LabelFrame(self.root, text="Serial Control", padding=10)
        frame.place(x=1200, y=520, width=240, height=100)
        
        ttk.Button(frame, text="Comport Open").place(x=0, y=0, width=75, height=25)
        ttk.Button(frame, text="OFF").place(x=80, y=0, width=75, height=25)
        ttk.Button(frame, text="ON").place(x=160, y=0, width=75, height=25)
    
    def create_status_frame(self):
        """Create status area"""
        frame = ttk.LabelFrame(self.root, text="Status & Log", padding=10)
        frame.place(x=1200, y=630, width=240, height=200)
        
        text_memo = tk.Text(frame, width=28, height=10, wrap=tk.WORD)
        text_memo.place(x=0, y=0, width=200, height=160)
        text_memo.insert(tk.END, "Layout test started...\nAll frames should be visible\nNo overlapping controls")
    
    def add_debug_borders(self):
        """Add colored borders to frames for debugging"""
        # This helps visualize frame boundaries
        debug_frame = tk.Frame(self.root, bg='red', height=2)
        debug_frame.place(x=10, y=93, width=460)  # Below connection frame
        
        debug_frame2 = tk.Frame(self.root, bg='blue', height=2)
        debug_frame2.place(x=8, y=350, width=460)  # Below register frame
        
        # Add frame size labels
        size_label = tk.Label(self.root, text="Connection: 460x110", bg='yellow', font=('Arial', 8))
        size_label.place(x=10, y=120)

        size_label2 = tk.Label(self.root, text="Register: 460x250", bg='yellow', font=('Arial', 8))
        size_label2.place(x=8, y=377)

        size_label3 = tk.Label(self.root, text="Parameters: 460x400", bg='yellow', font=('Arial', 8))
        size_label3.place(x=8, y=785)
    
    def run(self):
        """Run the test application"""
        print("Layout Test Application")
        print("=" * 30)
        print("Window size: 1453x900")
        print("Check that all controls are visible and not overlapping")
        print("Close the window when done testing")
        print()
        
        self.root.mainloop()

if __name__ == "__main__":
    app = LayoutTestApp()
    app.run()
