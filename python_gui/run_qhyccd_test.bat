@echo off
title QHYCCD Sensor Performance Test Tools
echo QHYCCD Sensor Performance Test Tools - Python GUI
echo ==================================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7 or higher from https://python.org
    pause
    exit /b 1
)

REM Change to script directory
cd /d "%~dp0"

echo.
echo Choose an option:
echo 1. Run with dependency check (recommended)
echo 2. Run directly (skip dependency check)
echo 3. Test dependencies only
echo 4. Install dependencies
echo 5. Test GUI layout only
echo 6. Test QHYCCD DLL loading
echo 7. Fix DLL issues (64-bit)
echo 8. Test ControlID fix
echo 9. Test exposure adjustment fix
echo 10. Test button logic
echo 11. Check button logic fix
echo 12. Exit
echo.
set /p choice="Enter your choice (1-12): "

if "%choice%"=="1" (
    echo Running with dependency check...
    python run_app.py
) else if "%choice%"=="2" (
    echo Running directly...
    python simple_run.py
) else if "%choice%"=="3" (
    echo Testing dependencies...
    python test_dependencies.py
    pause
) else if "%choice%"=="4" (
    echo Installing dependencies...
    python install_deps.py
) else if "%choice%"=="5" (
    echo Testing GUI layout...
    python test_layout.py
    pause
) else if "%choice%"=="6" (
    echo Testing QHYCCD DLL...
    python test_dll.py
) else if "%choice%"=="7" (
    echo Fixing DLL issues...
    python fix_dll.py
) else if "%choice%"=="8" (
    echo Testing ControlID fix...
    python test_controlid.py
) else if "%choice%"=="9" (
    echo Testing exposure adjustment fix...
    python test_exposure_adj.py
) else if "%choice%"=="10" (
    echo Testing button logic...
    python test_button_logic.py
) else if "%choice%"=="11" (
    echo Checking button logic fix...
    python fix_button_logic.py
) else if "%choice%"=="12" (
    exit /b 0
) else (
    echo Invalid choice. Running with dependency check...
    python run_app.py
)

REM Keep window open if there was an error
if errorlevel 1 (
    echo.
    echo Application exited with error
    pause
)
