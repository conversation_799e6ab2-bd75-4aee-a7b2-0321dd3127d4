// CodeGear C++Builder
// Copyright (c) 1995, 2014 by Embarcadero Technologies, Inc.
// All rights reserved

// (DO NOT EDIT: machine generated header) 'Winsoft.FireMonkey.ComPortE.pas' rev: 28.00 (Windows)

#ifndef Winsoft_Firemonkey_ComporteHPP
#define Winsoft_Firemonkey_ComporteHPP

#pragma delphiheader begin
#pragma option push
#pragma option -w-      // All warnings off
#pragma option -Vx      // Zero-length empty class member 
#pragma pack(push,8)
#include <System.hpp>	// Pascal unit
#include <SysInit.hpp>	// Pascal unit

//-- user supplied -----------------------------------------------------------

namespace Winsoft
{
namespace Firemonkey
{
namespace Comporte
{
//-- type declarations -------------------------------------------------------
//-- var, const, procedure ---------------------------------------------------
extern DELPHI_PACKAGE void __fastcall Register(void);
}	/* namespace Comporte */
}	/* namespace Firemonkey */
}	/* namespace Winsoft */
#if !defined(DELPHIHEADER_NO_IMPLICIT_NAMESPACE_USE) && !defined(NO_USING_NAMESPACE_WINSOFT_FIREMONKEY_COMPORTE)
using namespace Winsoft::Firemonkey::Comporte;
#endif
#if !defined(DELPHIHEADER_NO_IMPLICIT_NAMESPACE_USE) && !defined(NO_USING_NAMESPACE_WINSOFT_FIREMONKEY)
using namespace Winsoft::Firemonkey;
#endif
#if !defined(DELPHIHEADER_NO_IMPLICIT_NAMESPACE_USE) && !defined(NO_USING_NAMESPACE_WINSOFT)
using namespace Winsoft;
#endif
#pragma pack(pop)
#pragma option pop

#pragma delphiheader end.
//-- end unit ----------------------------------------------------------------
#endif	// Winsoft_Firemonkey_ComporteHPP
