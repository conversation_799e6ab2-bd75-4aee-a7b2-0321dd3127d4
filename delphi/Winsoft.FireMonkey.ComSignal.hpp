// CodeGear C++Builder
// Copyright (c) 1995, 2014 by Embarcadero Technologies, Inc.
// All rights reserved

// (DO NOT EDIT: machine generated header) 'Winsoft.FireMonkey.ComSignal.pas' rev: 28.00 (Windows)

#ifndef Winsoft_Firemonkey_ComsignalHPP
#define Winsoft_Firemonkey_ComsignalHPP

#pragma delphiheader begin
#pragma option push
#pragma option -w-      // All warnings off
#pragma option -Vx      // Zero-length empty class member 
#pragma pack(push,8)
#include <System.hpp>	// Pascal unit
#include <SysInit.hpp>	// Pascal unit
#include <System.SysUtils.hpp>	// Pascal unit
#include <System.Classes.hpp>	// Pascal unit
#include <System.UITypes.hpp>	// Pascal unit
#include <System.UIConsts.hpp>	// Pascal unit
#include <FMX.Colors.hpp>	// Pascal unit
#include <FMX.Types.hpp>	// Pascal unit
#include <Winsoft.FireMonkey.ComPort.hpp>	// Pascal unit

//-- user supplied -----------------------------------------------------------

namespace Winsoft
{
namespace Firemonkey
{
namespace Comsignal
{
//-- type declarations -------------------------------------------------------
enum DECLSPEC_DENUM TSignal : unsigned char { siNone, siBreak, siRxChar, siTxChar, siCTS, siDSR, siEvent1, siEvent2, siLineError, siPrinterError, siRing, siRLSD, siRx80PercFull, siRxFlag, siTxEmpty };

class DELPHICLASS TFComSignal;
class PASCALIMPLEMENTATION TFComSignal : public System::Classes::TComponent
{
	typedef System::Classes::TComponent inherited;
	
private:
	System::Uitypes::TAlphaColor FColorOff;
	System::Uitypes::TAlphaColor FColorOn;
	Winsoft::Firemonkey::Comport::TFComPort* FComPort;
	Fmx::Colors::TColorBox* FColorBox;
	int FDelay;
	TSignal FSignal;
	bool FSignalValue;
	Fmx::Types::TTimer* FTimer;
	bool FWriting;
	Winsoft::Firemonkey::Comport::TOpenCloseEvent FAfterClose;
	Winsoft::Firemonkey::Comport::TOpenCloseEvent FAfterOpen;
	Winsoft::Firemonkey::Comport::TReadWriteEvent FAfterWrite;
	Winsoft::Firemonkey::Comport::TReadWriteEvent FBeforeWrite;
	System::Classes::TNotifyEvent FOnNotifySignal;
	Winsoft::Firemonkey::Comport::TLineErrorEvent FOnLineError;
	System::Classes::TNotifyEvent FOnModemSignal;
	System::Classes::TNotifyEvent FOnSignal;
	System::UnicodeString __fastcall GetAbout(void);
	void __fastcall SetAbout(const System::UnicodeString Value);
	void __fastcall SetColorOff(System::Uitypes::TAlphaColor Value);
	void __fastcall SetComPort(Winsoft::Firemonkey::Comport::TFComPort* Value);
	void __fastcall SetColorBox(Fmx::Colors::TColorBox* Value);
	void __fastcall SetDelay(int Value);
	void __fastcall SetSignalValue(bool Value);
	
protected:
	virtual void __fastcall Loaded(void);
	virtual void __fastcall Notification(System::Classes::TComponent* AComponent, System::Classes::TOperation Operation);
	void __fastcall UpdateColor(void);
	void __fastcall UpdateSignalValue(void);
	void __fastcall RetrieveEvents(void);
	void __fastcall RestoreEvents(void);
	void __fastcall StartTimer(void);
	void __fastcall AfterClose(Winsoft::Firemonkey::Comport::TFComPort* ComPort);
	void __fastcall AfterOpen(Winsoft::Firemonkey::Comport::TFComPort* ComPort);
	void __fastcall AfterWrite(System::TObject* Sender, void * Buffer, int Length, bool WaitOnCompletion);
	void __fastcall BeforeWrite(System::TObject* Sender, void * Buffer, int Length, bool WaitOnCompletion);
	void __fastcall OnNotifySignal(System::TObject* Sender);
	void __fastcall OnLineError(System::TObject* Sender, Winsoft::Firemonkey::Comport::TLineErrors LineErrors);
	void __fastcall OnModemSignal(System::TObject* Sender);
	void __fastcall OnTimer(System::TObject* Sender);
	
public:
	__fastcall virtual TFComSignal(System::Classes::TComponent* AOwner);
	__fastcall virtual ~TFComSignal(void);
	
__published:
	__property System::UnicodeString About = {read=GetAbout, write=SetAbout, stored=false};
	__property System::Uitypes::TAlphaColor ColorOff = {read=FColorOff, write=SetColorOff, default=-16744448};
	__property System::Uitypes::TAlphaColor ColorOn = {read=FColorOn, write=FColorOn, default=-16711936};
	__property Winsoft::Firemonkey::Comport::TFComPort* ComPort = {read=FComPort, write=SetComPort};
	__property Fmx::Colors::TColorBox* ColorBox = {read=FColorBox, write=SetColorBox};
	__property int Delay = {read=FDelay, write=SetDelay, nodefault};
	__property TSignal Signal = {read=FSignal, write=FSignal, nodefault};
	__property bool SignalValue = {read=FSignalValue, stored=false, nodefault};
	__property System::Classes::TNotifyEvent OnSignal = {read=FOnSignal, write=FOnSignal};
};


//-- var, const, procedure ---------------------------------------------------
}	/* namespace Comsignal */
}	/* namespace Firemonkey */
}	/* namespace Winsoft */
#if !defined(DELPHIHEADER_NO_IMPLICIT_NAMESPACE_USE) && !defined(NO_USING_NAMESPACE_WINSOFT_FIREMONKEY_COMSIGNAL)
using namespace Winsoft::Firemonkey::Comsignal;
#endif
#if !defined(DELPHIHEADER_NO_IMPLICIT_NAMESPACE_USE) && !defined(NO_USING_NAMESPACE_WINSOFT_FIREMONKEY)
using namespace Winsoft::Firemonkey;
#endif
#if !defined(DELPHIHEADER_NO_IMPLICIT_NAMESPACE_USE) && !defined(NO_USING_NAMESPACE_WINSOFT)
using namespace Winsoft;
#endif
#pragma pack(pop)
#pragma option pop

#pragma delphiheader end.
//-- end unit ----------------------------------------------------------------
#endif	// Winsoft_Firemonkey_ComsignalHPP
