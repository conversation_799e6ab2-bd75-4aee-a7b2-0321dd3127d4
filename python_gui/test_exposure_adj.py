#!/usr/bin/env python3
"""
Exposure Adjustment Test Script
Test the exposure time adjustment functionality without camera
"""

import sys
import os
import time
import numpy as np

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

class MockCamera:
    """Mock camera for testing"""
    
    def __init__(self):
        self.is_connected = True
        self.is_live = False
        self.current_exposure = 45000
        self.current_gain = 0
        
    def start_live(self):
        self.is_live = True
        return True
    
    def stop_live(self):
        self.is_live = False
        return True
    
    def set_parameter(self, param_id, value):
        if param_id == 8:  # CONTROL_EXPOSURE
            self.current_exposure = value
        elif param_id == 6:  # CONTROL_GAIN
            self.current_gain = value
        return True
    
    def get_live_frame(self):
        """Generate mock frame data based on current exposure"""
        # Simulate frame data that varies with exposure time
        base_signal = min(65535, max(1000, self.current_exposure // 10))
        
        # Add some noise
        noise = np.random.normal(0, 100, (100, 100))
        frame = np.clip(base_signal + noise, 0, 65535).astype(np.uint16)
        
        return frame

class MockSerial:
    """Mock serial controller"""
    
    def __init__(self):
        self.is_connected = True
    
    def send_flat_panel_command(self, command):
        print(f"Mock: Flat panel command '{command}' sent")
        return True

def test_exposure_adjustment():
    """Test exposure adjustment functionality"""
    print("Testing Exposure Time Adjustment")
    print("=" * 40)
    
    try:
        # Import required modules
        from image_processor import ImageProcessor
        from test_automation import TestAutomation, TestType
        
        # Create mock objects
        mock_camera = MockCamera()
        mock_serial = MockSerial()
        image_processor = ImageProcessor()
        
        # Create test automation
        test_automation = TestAutomation(mock_camera, mock_serial, image_processor)
        
        # Set up progress callback
        def progress_callback(message, progress):
            print(f"Progress ({progress}%): {message}")
        
        def result_callback(results):
            print("Results received:")
            for key, value in results.items():
                print(f"  {key}: {value}")
        
        test_automation.set_callbacks(
            progress_callback=progress_callback,
            result_callback=result_callback
        )
        
        print("✅ Test automation setup complete")
        
        # Test parameters
        params = {
            'flat_exposure_time': 45000,
            'target_rms': 30000,
            'max_rms': 60000,
            'min_rms': 25000,
            'roi_x': 10,
            'roi_y': 10,
            'roi_width': 80,
            'roi_height': 80
        }
        
        print(f"Starting exposure adjustment test...")
        print(f"Initial exposure: {params['flat_exposure_time']}us")
        print(f"Target RMS: {params['target_rms']}")
        
        # Start the test
        if test_automation.start_test(TestType.EXPOSURE_ADJUSTMENT, params):
            print("✅ Test started successfully")
            
            # Wait for test to complete
            max_wait = 30  # seconds
            wait_time = 0
            
            while test_automation.is_test_running() and wait_time < max_wait:
                time.sleep(1)
                wait_time += 1
                print(f"Waiting... ({wait_time}s)")
            
            if test_automation.is_test_running():
                print("⚠ Test still running after timeout, stopping...")
                test_automation.stop_current_test()
            else:
                print("✅ Test completed")
            
            # Get results
            results = test_automation.get_test_results()
            if results:
                print("\nFinal Results:")
                for key, value in results.items():
                    print(f"  {key}: {value}")
            else:
                print("No results available")
                
        else:
            print("❌ Failed to start test")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_variable_initialization():
    """Test that all variables are properly initialized"""
    print("\nTesting Variable Initialization")
    print("=" * 40)
    
    try:
        from test_automation import TestAutomation
        
        # Create minimal test setup
        mock_camera = MockCamera()
        mock_serial = MockSerial()
        
        # Import image processor
        from image_processor import ImageProcessor
        image_processor = ImageProcessor()
        
        test_automation = TestAutomation(mock_camera, mock_serial, image_processor)
        
        # Check default parameters
        default_params = test_automation.default_params
        required_keys = [
            'flat_exposure_time',
            'target_rms',
            'max_rms', 
            'min_rms',
            'roi_x',
            'roi_y',
            'roi_width',
            'roi_height'
        ]
        
        print("Checking default parameters:")
        for key in required_keys:
            if key in default_params:
                print(f"✅ {key}: {default_params[key]}")
            else:
                print(f"❌ Missing: {key}")
                return False
        
        print("✅ All required parameters present")
        return True
        
    except Exception as e:
        print(f"❌ Variable initialization test failed: {e}")
        return False

def main():
    """Main test function"""
    print("Exposure Adjustment Fix Test")
    print("=" * 50)
    
    all_tests_passed = True
    
    # Run tests
    tests = [
        ("Variable Initialization", test_variable_initialization),
        ("Exposure Adjustment", test_exposure_adjustment)
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*10} {test_name} {'='*10}")
        if not test_func():
            all_tests_passed = False
    
    # Summary
    print("\n" + "=" * 50)
    if all_tests_passed:
        print("🎉 ALL TESTS PASSED!")
        print("The exposure adjustment fix is working correctly.")
        print("You can now use the ExpTimeAdj button safely.")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please check the errors above.")
    
    print("\n" + "=" * 50)
    input("Press Enter to exit...")
    return 0 if all_tests_passed else 1

if __name__ == "__main__":
    sys.exit(main())
