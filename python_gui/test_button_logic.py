#!/usr/bin/env python3
"""
Button Logic Test Script
Test the camera connection button logic without actual camera
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

class MockCamera:
    """Mock camera for testing button logic"""
    
    def __init__(self):
        self.is_connected = False
        self.camera_id = "Mock-QHY5II-12345"
        self.read_modes = ["Mode1", "Mode2", "Mode3"]
        self.is_live = False
    
    def initialize_resources(self):
        print("Mock: Initializing QHYCCD resources")
        return True
    
    def scan_cameras(self):
        print("Mock: Scanning cameras")
        return 1  # Found 1 camera
    
    def get_camera_id(self, index):
        print(f"Mock: Getting camera ID for index {index}")
        return self.camera_id
    
    def connect_camera(self, camera_id=None):
        print(f"Mock: Connecting to camera {camera_id or 'auto'}")
        self.is_connected = True
        return True
    
    def disconnect_camera(self):
        print("Mock: Disconnecting camera")
        self.is_connected = False
        return True
    
    def init_camera(self):
        print("Mock: Initializing camera")
        return True
    
    def set_resolution(self, x, y, w, h):
        print(f"Mock: Setting resolution {w}x{h} at ({x},{y})")
        return True
    
    def set_read_mode(self, mode):
        print(f"Mock: Setting read mode {mode}")
        return True
    
    def set_parameter(self, param_id, value):
        print(f"Mock: Setting parameter {param_id} = {value}")
        return True

class ButtonLogicTest:
    """Test application for button logic"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Button Logic Test")
        self.root.geometry("600x400")
        
        # Mock camera
        self.camera = MockCamera()
        
        # Create UI
        self.create_ui()
        
        # Log area
        self.log_messages = []
    
    def create_ui(self):
        """Create test UI"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Connection frame
        conn_frame = ttk.LabelFrame(main_frame, text="Camera Connection Test", padding=10)
        conn_frame.pack(fill=tk.X, pady=10)
        
        # ReadMode button
        self.btn_read_mode = ttk.Button(conn_frame, text="ReadMode", command=self.read_mode_connect)
        self.btn_read_mode.pack(side=tk.LEFT, padx=5)
        
        # Read mode combo
        ttk.Label(conn_frame, text="Read Mode:").pack(side=tk.LEFT, padx=5)
        self.combo_read_mode = ttk.Combobox(conn_frame, width=15, state="readonly")
        self.combo_read_mode.pack(side=tk.LEFT, padx=5)
        
        # Total modes label
        self.label_total_modes = ttk.Label(conn_frame, text="Total ReadModes: 0")
        self.label_total_modes.pack(side=tk.LEFT, padx=10)
        
        # Connect button
        self.btn_connect = ttk.Button(conn_frame, text="连接相机", command=self.connect_camera)
        self.btn_connect.pack(side=tk.RIGHT, padx=5)
        
        # Close button
        self.btn_close_camera = ttk.Button(conn_frame, text="关闭相机", command=self.close_camera)
        self.btn_close_camera.pack(side=tk.RIGHT, padx=5)
        
        # Status frame
        status_frame = ttk.LabelFrame(main_frame, text="Status", padding=10)
        status_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Log area
        self.text_log = tk.Text(status_frame, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.text_log.yview)
        self.text_log.configure(yscrollcommand=scrollbar.set)
        
        self.text_log.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Test buttons
        test_frame = ttk.Frame(main_frame)
        test_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(test_frame, text="Test Sequence", command=self.test_sequence).pack(side=tk.LEFT, padx=5)
        ttk.Button(test_frame, text="Clear Log", command=self.clear_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(test_frame, text="Reset State", command=self.reset_state).pack(side=tk.LEFT, padx=5)
        
        # Initial log
        self.add_log("Button Logic Test Started")
        self.add_log("Camera state: disconnected")
    
    def add_log(self, message):
        """Add message to log"""
        import time
        timestamp = time.strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}\n"
        self.text_log.insert(tk.END, full_message)
        self.text_log.see(tk.END)
        print(message)  # Also print to console
    
    def clear_log(self):
        """Clear log area"""
        self.text_log.delete(1.0, tk.END)
    
    def reset_state(self):
        """Reset all states"""
        self.camera.is_connected = False
        self._update_connection_ui_disconnected()
        self.add_log("State reset to disconnected")
    
    # ==================== Button Logic Methods ====================
    
    def read_mode_connect(self):
        """ReadMode button logic"""
        self.add_log("ReadMode button clicked")
        
        try:
            if not self.camera.is_connected:
                self.add_log("Camera not connected, connecting...")
                
                # Initialize resources
                if not self.camera.initialize_resources():
                    self.add_log("ERROR: Failed to initialize QHYCCD resources")
                    return

                # Scan and connect to first available camera
                num_cameras = self.camera.scan_cameras()
                if num_cameras > 0:
                    camera_id = self.camera.get_camera_id(0)
                    if self.camera.connect_camera(camera_id):
                        self._update_connection_ui_connected()
                        self.add_log(f"ReadMode: Connected to {camera_id}")

                        # Initialize camera
                        self.camera.init_camera()
                        self.camera.set_resolution(0, 0, 1000, 1000)
                        self.setup_default_camera_params()
                    else:
                        self.add_log("ERROR: Failed to connect to camera")
                else:
                    self.add_log("ERROR: No cameras found")
            else:
                self.add_log("Camera already connected")
                
        except Exception as e:
            self.add_log(f"ERROR: ReadMode connection error: {e}")
    
    def connect_camera(self):
        """Connect camera button logic"""
        self.add_log("Connect Camera button clicked")
        
        try:
            # Check current button text to determine action
            current_text = self.btn_connect.cget("text")
            self.add_log(f"Current button text: '{current_text}'")
            self.add_log(f"Camera connected state: {self.camera.is_connected}")
            
            if not self.camera.is_connected or current_text == "连接相机":
                # Connect camera
                self.add_log("Action: Connecting camera")
                
                if not self.camera.is_connected:
                    # Initialize resources
                    if not self.camera.initialize_resources():
                        self.add_log("ERROR: Failed to initialize QHYCCD resources")
                        return

                    # Connect to camera
                    if self.camera.connect_camera():
                        self._update_connection_ui_connected()
                        self.add_log(f"Connected to camera: {self.camera.camera_id}")
                        
                        # Initialize camera
                        self.camera.init_camera()
                        self.camera.set_resolution(0, 0, 1000, 1000)
                        self.setup_default_camera_params()
                    else:
                        self.add_log("ERROR: Failed to connect to camera")
                else:
                    # Camera already connected, just update UI
                    self._update_connection_ui_connected()
                    self.add_log("Camera already connected, UI updated")
            else:
                # Disconnect camera
                self.add_log("Action: Disconnecting camera")
                
                if self.camera.disconnect_camera():
                    self._update_connection_ui_disconnected()
                    self.add_log("Camera disconnected")
                else:
                    self.add_log("ERROR: Failed to disconnect camera")

        except Exception as e:
            self.add_log(f"ERROR: Camera connection error: {e}")
    
    def close_camera(self):
        """Close camera button logic"""
        self.add_log("Close Camera button clicked")
        
        try:
            if self.camera.is_connected:
                if self.camera.disconnect_camera():
                    self._update_connection_ui_disconnected()
                    self.add_log("Camera closed")
                else:
                    self.add_log("ERROR: Failed to close camera")
            else:
                self.add_log("No camera connected")
                
        except Exception as e:
            self.add_log(f"ERROR: Close camera error: {e}")
    
    def _update_connection_ui_connected(self):
        """Update UI when camera is connected"""
        self.add_log("UI: Updating to connected state")
        self.btn_connect.config(text="断开相机")
        
        # Update read modes
        self.combo_read_mode['values'] = self.camera.read_modes
        if self.camera.read_modes:
            self.combo_read_mode.current(0)
            self.camera.set_read_mode(0)
        
        self.label_total_modes.config(text=f"Total ReadModes: {len(self.camera.read_modes)}")
        
        # Update ReadMode button
        self.btn_read_mode.config(text="Connected")
    
    def _update_connection_ui_disconnected(self):
        """Update UI when camera is disconnected"""
        self.add_log("UI: Updating to disconnected state")
        self.btn_connect.config(text="连接相机")
        self.btn_read_mode.config(text="ReadMode")
        self.combo_read_mode['values'] = []
        self.label_total_modes.config(text="Total ReadModes: 0")
    
    def setup_default_camera_params(self):
        """Setup default camera parameters"""
        self.add_log("Setting up default camera parameters")
        # Mock parameter setup
        params = [
            (36, 0),    # DDR
            (7, 30),    # OFFSET
            (12, 0),    # USBTRAFFIC
            (10, 16),   # TRANSFERBIT
            (8, 45000), # EXPOSURE
            (6, 0),     # GAIN
        ]
        
        for param_id, value in params:
            self.camera.set_parameter(param_id, value)
    
    def test_sequence(self):
        """Run automated test sequence"""
        self.add_log("=== Starting Automated Test Sequence ===")
        
        # Test 1: ReadMode button first
        self.add_log("Test 1: Click ReadMode button")
        self.read_mode_connect()
        
        self.root.after(1000, self._test_sequence_step2)
    
    def _test_sequence_step2(self):
        """Test sequence step 2"""
        # Test 2: Connect button (should show disconnect)
        self.add_log("Test 2: Click Connect Camera button (should disconnect)")
        self.connect_camera()
        
        self.root.after(1000, self._test_sequence_step3)
    
    def _test_sequence_step3(self):
        """Test sequence step 3"""
        # Test 3: Connect button again (should connect)
        self.add_log("Test 3: Click Connect Camera button (should connect)")
        self.connect_camera()
        
        self.root.after(1000, self._test_sequence_step4)
    
    def _test_sequence_step4(self):
        """Test sequence step 4"""
        # Test 4: Close button
        self.add_log("Test 4: Click Close Camera button")
        self.close_camera()
        
        self.add_log("=== Test Sequence Complete ===")
    
    def run(self):
        """Run the test application"""
        self.root.mainloop()

def main():
    """Main function"""
    print("Button Logic Test Application")
    print("=" * 40)
    print("This tests the camera connection button logic")
    print("without requiring actual camera hardware.")
    print()
    
    app = ButtonLogicTest()
    app.run()

if __name__ == "__main__":
    main()
