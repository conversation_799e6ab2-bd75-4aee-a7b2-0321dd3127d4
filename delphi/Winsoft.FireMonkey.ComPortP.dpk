package Winsoft.FireMonkey.ComPortP;

{$R *.res}
{$R 'Winsoft.FireMonkey.ComPort.DCR'}
{$ALIGN 8}
{$ASSERTIONS OFF}
{$BOOLEVAL OFF}
{$DEBUGINFO OFF}
{$EXTENDEDSYNTAX ON}
{$IMPORTE<PERSON><PERSON><PERSON> ON}
{$IOCHECKS ON}
{$LOCALSYMBOLS OFF}
{$LONGSTRINGS ON}
{$OPENSTRINGS ON}
{$OPTIMIZATION ON}
{$OVERFLOWCHECKS OFF}
{$RANGECHECKS OFF}
{$REFERENCEINFO OFF}
{$SAFEDIVIDE OFF}
{$STACKFRAMES OFF}
{$TYPEDADDRESS OFF}
{$VARSTRINGCHECKS ON}
{$WRITEABLECONST OFF}
{$MINENUMSIZE 1}
{$DESCRIPTION 'Winsoft ComPort for FireMonkey'}
{$DESIGNONLY}
{$IMPLICITBUILD ON}

requires
  rtl,
  fmx,
  designide;

contains
  Winsoft.FireMonkey.ComPort in 'Winsoft.FireMonkey.ComPort.pas',
  Winsoft.FireMonkey.ComPortE in 'Winsoft.FireMonkey.ComPortE.pas',
  Winsoft.FireMonkey.ComSignal in 'Winsoft.FireMonkey.ComSignal.pas',
  Winsoft.FireMonkey.ComSignalE in 'Winsoft.FireMonkey.ComSignalE.pas';

end.