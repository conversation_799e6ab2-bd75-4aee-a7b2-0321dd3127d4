//---------------------------------------------------------------------------

#ifndef Unit1H
#define Unit1H
//---------------------------------------------------------------------------
#include <Classes.hpp>
#include <Controls.hpp>
#include <StdCtrls.hpp>
#include <Forms.hpp>
#include <Vcl.ComCtrls.hpp>
#include <Vcl.ExtCtrls.hpp>
#include "ComPort.hpp"
#include <Vcl.Grids.hpp>
//---------------------------------------------------------------------------
class TForm1 : public TForm
{
__published:	// IDE-managed Components
	TButton *Button1;
	TButton *Button2;
	TEdit *Edit1;
	TEdit *Edit2;
	TButton *Button3;
	TEdit *Edit3;
	TButton *Button4;
	TButton *Button5;
	TButton *Button6;
	TButton *Button7;
	TButton *Button8;
	TButton *Button9;
	TButton *Button10;
	TButton *Button11;
	TButton *Button12;
	TButton *Button13;
	TButton *Button14;
	TButton *Button15;
	TButton *Button16;
	TButton *Button17;
	TButton *Button18;
	TButton *Button19;
	TButton *Button20;
	TEdit *Edit4;
	TScrollBar *ScrollBar1;
	TScrollBar *ScrollBar2;
	TButton *Button21;
	TButton *Button22;
	TButton *ButtonConnectAndMode;
	TButton *Button24;
	TButton *Button25;
	TEdit *Edit5;
	TScrollBar *ScrollBar3;
	TButton *Button26;
	TButton *Button27;
	TButton *SHOWIMG;
	TButton *STOPSHOW;
	TButton *Button28;
	TButton *Button29;
	TButton *Button30;
	TButton *Button32;
	TButton *Button33;
	TTrackBar *TrackBar1;
	TTrackBar *TrackBar2;
	TTrackBar *TrackBar3;
	TTrackBar *TrackBar4;
	TTrackBar *TrackBar5;
	TTrackBar *TrackBar6;
	TTrackBar *trckbr1;
	TLabel *Label1;
	TLabel *Label2;
	TLabel *Label3;
	TLabel *Label4;
	TLabel *Label5;
	TLabel *Label6;
	TLabel *Label7;
	TTrackBar *TrackBar7;
	TLabel *Label8;
	TTrackBar *TrackBar8;
	TLabel *LabelOffset;
	TTrackBar *TrackBar9;
	TLabel *Label10;
	TTrackBar *TrackBar10;
	TLabel *Label11;
	TTrackBar *TrackBar11;
	TLabel *Label12;
	TCheckBox *CheckBox1;
	TTrackBar *TrackBar12;
	TCheckBox *CheckBox2;
	TTrackBar *TrackBar13;
	TTrackBar *TrackBar14;
	TTrackBar *TrackBar15;
	TTrackBar *TrackBar16;
	TTrackBar *TrackBar17;
	TCheckBox *CheckBox3;
	TTrackBar *TrackBar20;
	TLabel *Label15;
	TLabel *Label17;
	TLabel *Label20;
	TLabel *Label22;
	TCheckBox *CheckBox6;
	TButton *Button35;
	TButton *Button36;
	TTimer *Timer1;
	TButton *Button45;
	TMemo *Memo1;
	TButton *ButtonTest;
	TButton *ButtonAETA;
	TComPort *FComPort1;
	TButton *Button46;
	TButton *Button47;
	TButton *Button48;
	TButton *Button49;
	TScrollBar *ScrollBar5;
	TScrollBar *ScrollBar6;
	TScrollBar *ScrollBar7;
	TScrollBar *ScrollBar8;
	TButton *ButtonGain;
	TLabel *Label31;
	TLabel *Label32;
	TLabel *Label33;
	TLabel *Label34;
	TButton *Button50;
	TButton *Button51;
	TButton *Button52;
	TButton *Button53;
	TButton *Button54;
	TButton *Button55;
	TButton *Button56;
	TButton *Button57;
	TButton *Button58;
	TButton *Button59;
	TButton *Button60;
	TButton *Button61;
	TButton *Button62;
	TButton *Button63;
	TButton *Button64;
	TButton *Button65;
	TButton *Button66;
	TButton *Button67;
	TButton *Button68;
	TButton *Button69;
	TButton *Button70;
	TImage *ImageFlat1;
	TImage *ImageFlat2;
	TImage *ImageBias1;
	TImage *ImageBias2;
	TImage *ImagePreview;
	TComboBox *ComboBoxComPort;
	TMemo *MemoGain;
	TMemo *MemoSystemGain;
	TMemo *MemoFullWell;
	TMemo *MemoReadoutNoise;
	TComboBox *ComboBoxReadMode;
	TLabel *LabelTotalReadMode;
	TEdit *Edit6;
	TEdit *Edit7;
	TLabel *Label9;
	TLabel *Label13;
	TMemo *MemoReadoutNoise2;
	void __fastcall Button1Click(TObject *Sender);
	void __fastcall Button3Click(TObject *Sender);
	void __fastcall Button20Click(TObject *Sender);
	void __fastcall Button19Click(TObject *Sender);
	void __fastcall Button18Click(TObject *Sender);
	void __fastcall Button17Click(TObject *Sender);
	void __fastcall Button16Click(TObject *Sender);
	void __fastcall Button15Click(TObject *Sender);
	void __fastcall Button14Click(TObject *Sender);
	void __fastcall Button13Click(TObject *Sender);
	void __fastcall Button12Click(TObject *Sender);
	void __fastcall Button11Click(TObject *Sender);
	void __fastcall Button10Click(TObject *Sender);
	void __fastcall Button9Click(TObject *Sender);
	void __fastcall Button8Click(TObject *Sender);
	void __fastcall Button7Click(TObject *Sender);
	void __fastcall Button6Click(TObject *Sender);
	void __fastcall Button5Click(TObject *Sender);
	void __fastcall Button4Click(TObject *Sender);
	void __fastcall Button21Click(TObject *Sender);
	void __fastcall Button26Click(TObject *Sender);
	void __fastcall ScrollBar1Change(TObject *Sender);
	void __fastcall ScrollBar2Change(TObject *Sender);
	void __fastcall ScrollBar3Change(TObject *Sender);
	void __fastcall Button27Click(TObject *Sender);
	void __fastcall STOPSHOWClick(TObject *Sender);
	void __fastcall Button2Click(TObject *Sender);
	void __fastcall Button28Click(TObject *Sender);
	void __fastcall Button22Click(TObject *Sender);
	void __fastcall TrackBar2Change(TObject *Sender);
	void __fastcall TrackBar3Change(TObject *Sender);
	void __fastcall TrackBar4Change(TObject *Sender);
	void __fastcall TrackBar5Change(TObject *Sender);
	void __fastcall TrackBar6Change(TObject *Sender);
	void __fastcall TrackBar1Change(TObject *Sender);
	void __fastcall FormCreate(TObject *Sender);
	void __fastcall FormClose(TObject *Sender, TCloseAction &Action);
	void __fastcall trckbr1Change(TObject *Sender);
	void __fastcall TrackBar7Change(TObject *Sender);
	void __fastcall TrackBar8Change(TObject *Sender);
	void __fastcall TrackBar9Change(TObject *Sender);
	void __fastcall TrackBar10Change(TObject *Sender);
	void __fastcall TrackBar11Change(TObject *Sender);
	void __fastcall Button32Click(TObject *Sender);
	void __fastcall CheckBox1Click(TObject *Sender);
	void __fastcall TrackBar12Change(TObject *Sender);
	void __fastcall CheckBox2Click(TObject *Sender);
	void __fastcall TrackBar13Change(TObject *Sender);
	void __fastcall TrackBar14Change(TObject *Sender);
	void __fastcall TrackBar15Change(TObject *Sender);
	void __fastcall TrackBar16Change(TObject *Sender);
	void __fastcall TrackBar17Change(TObject *Sender);
	void __fastcall CheckBox3Click(TObject *Sender);
	void __fastcall Button25Click(TObject *Sender);
	void __fastcall TrackBar20Change(TObject *Sender);
	void __fastcall CheckBox6Click(TObject *Sender);
	void __fastcall Button33Click(TObject *Sender);
	void __fastcall Button24Click(TObject *Sender);
	void __fastcall Button35Click(TObject *Sender);
	void __fastcall Button39Click(TObject *Sender);
	void __fastcall Timer1Timer(TObject *Sender);
	void __fastcall Button45Click(TObject *Sender);
	void __fastcall ButtonTestClick(TObject *Sender);
	void __fastcall ButtonAETAClick(TObject *Sender);
	void __fastcall Button46Click(TObject *Sender);
	void __fastcall Button47Click(TObject *Sender);
	void __fastcall Button48Click(TObject *Sender);
	void __fastcall Button49Click(TObject *Sender);
	void __fastcall ButtonGainClick(TObject *Sender);
	void __fastcall ScrollBar5Change(TObject *Sender);
	void __fastcall ScrollBar6Change(TObject *Sender);
	void __fastcall ScrollBar7Change(TObject *Sender);
	void __fastcall ScrollBar8Change(TObject *Sender);
	void __fastcall Button50Click(TObject *Sender);
	void __fastcall Button51Click(TObject *Sender);
	void __fastcall Button52Click(TObject *Sender);
	void __fastcall Button53Click(TObject *Sender);
	void __fastcall Button54Click(TObject *Sender);
	void __fastcall Button70Click(TObject *Sender);
	void __fastcall Memo1Change(TObject *Sender);
	void __fastcall ComboBoxComPortChange(TObject *Sender);
	void __fastcall Button30Click(TObject *Sender);
	void __fastcall ButtonConnectAndModeClick(TObject *Sender);
	void __fastcall Button29Click(TObject *Sender);
	void __fastcall Edit6Change(TObject *Sender);
	void __fastcall Edit7Change(TObject *Sender);
	void __fastcall Button55Click(TObject *Sender);
	void __fastcall Button56Click(TObject *Sender);
	void __fastcall Button57Click(TObject *Sender);
	void __fastcall Button58Click(TObject *Sender);
	void __fastcall Button59Click(TObject *Sender);
	void __fastcall Button60Click(TObject *Sender);
	void __fastcall Button61Click(TObject *Sender);
	void __fastcall Button62Click(TObject *Sender);
	void __fastcall Button63Click(TObject *Sender);
	void __fastcall Button64Click(TObject *Sender);
	void __fastcall Button65Click(TObject *Sender);
	void __fastcall Button66Click(TObject *Sender);
	void __fastcall Button67Click(TObject *Sender);
	void __fastcall Button68Click(TObject *Sender);


private:	// User declarations
public:		// User declarations
	__fastcall TForm1(TComponent* Owner);
};
//---------------------------------------------------------------------------
extern PACKAGE TForm1 *Form1;
//---------------------------------------------------------------------------
#endif
