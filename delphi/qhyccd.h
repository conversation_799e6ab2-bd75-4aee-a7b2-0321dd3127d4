//---------------------------------------------------------------------------

#ifndef qhyccdH
#define qhyccdH
//---------------------------------------------------------------------------

enum CONTROL_ID
{
	CONTROL_BRIGHTNESS = 0, /* image brightness */
	CONTROL_CONTRAST,       /* image contrast */
	CONTROL_WBR,            /* red of white balance */
	CONTROL_WBB,            /* blue of white balance */
	CONTROL_WBG,            /* the green of white balance */
	CONTROL_GAMMA,          /* screen gamma */
	CONTROL_GAIN,           /* camera gain */
	CONTROL_OFFSET,         /* camera offset */
	CONTROL_EXPOSURE,       /* expose time */
	CONTROL_SPEED,          /* transfer speed */
	CONTROL_TRANSFERBIT,    /* image depth bits */
	CONTROL_CHANNELS,       /* image channels */
	CONTROL_USBTRAFFIC,     /* hblank */
	CONTROL_ROWNOISERE,     /* row denoise */
	CONTROL_CURTEMP,        /* current cmos or ccd temprature */
	CONTROL_CURPWM,         /* current cool pwm */
	CONTROL_MANULPWM,       /* set the cool pwm */
	CONTROL_CFWPORT,        /* control camera color filter wheel port */
	CONTROL_COOLER,
	CONTROL_ST4PORT,
	CAM_COLOR,
	CAM_BIN1X1MODE,         /* check if camera has bin1x1 mode */
	CAM_BIN2X2MODE,         /* check if camera has bin2x2 mode */
	CAM_BIN3X3MODE,         /* check if camera has bin3x3 mode */
	CAM_BIN4X4MODE,         /* check if camera has bin4x4 mode */

	CAM_MECHANICALSHUTTER,
	CONTROL_DDR
};

#ifdef __cplusplus
extern "C" {
#endif

typedef unsigned int     (__stdcall *FuncInitQHYCCDResource)(void);
typedef unsigned int     (__stdcall *FuncReleaseQHYCCDResource)(void);
typedef unsigned int     (__stdcall *FuncScanQHYCCD)(void);
typedef unsigned int     (__stdcall *FuncGetQHYCCDId)(unsigned int, char *);
typedef void*   (__stdcall *FuncOpenQHYCCD)(char *);
typedef unsigned int     (__stdcall *FuncCloseQHYCCD)(void*);
typedef unsigned int     (__stdcall *FuncInitQHYCCD)(void*);
typedef unsigned int     (__stdcall *FuncSetQHYCCDParam)(void*, CONTROL_ID, double);
typedef unsigned int     (__stdcall *FuncSetQHYCCDBinMode)(void*, unsigned int, unsigned int);
typedef unsigned int     (__stdcall *FuncGetQHYCCDMemLength)(void*);
typedef unsigned int     (__stdcall *FuncExpQHYCCDSingleFrame)(void*);
typedef unsigned int     (__stdcall *FuncGetQHYCCDSingleFrame)(void*, unsigned int *, unsigned int *, unsigned int *, unsigned int *, unsigned char *);
typedef unsigned int     (__stdcall *FuncCancelQHYCCDExposingAndReadout)(void*);
typedef unsigned int     (__stdcall *FuncBeginQHYCCDLive)(void*);
typedef unsigned int     (__stdcall *FuncGetQHYCCDLiveFrame)(void*, unsigned int *, unsigned int *, unsigned int *, unsigned int *, unsigned char *);
typedef unsigned int     (__stdcall *FuncSetQHYCCDStreamMode)(void *, unsigned char);
typedef unsigned int     (__stdcall *FuncSetQHYCCDResolution)(void *, unsigned int, unsigned int, unsigned int, unsigned int);
typedef unsigned int     (__stdcall *FuncStopQHYCCDLive)(void *);
typedef unsigned int     (__stdcall *FuncIsQHYCCDControlAvailable)(void *, CONTROL_ID);
typedef unsigned int     (__stdcall *FuncGetQHYCCDChipInfo)(void *, double *, double *, unsigned int *, unsigned int *, double *, double *, unsigned int *);
typedef unsigned int     (__stdcall *FuncGetQHYCCDParam)(void*, CONTROL_ID);
typedef unsigned int     (__stdcall *FuncQHYCCDVendRequestWrite)(void*, unsigned char,unsigned short,unsigned short,unsigned int ,unsigned char *);
typedef unsigned int     (__stdcall *FuncQHYCCDVendRequestRead)(void*, unsigned char,unsigned short,unsigned short,unsigned int ,unsigned char *);
typedef unsigned int     (__stdcall *FuncQHYCCDReadUSB_SYNC)(void*, unsigned char,unsigned int,unsigned char *,unsigned int);

typedef unsigned int     (__stdcall *FuncGetQHYCCDNumberOfReadModes)(void*,unsigned int *);
typedef unsigned int     (__stdcall *FuncGetQHYCCDReadModeResolution)(void*,unsigned int, unsigned int *, unsigned int *);
typedef unsigned int     (__stdcall *FuncGetQHYCCDReadModeName)(void*,unsigned int, char* );
typedef unsigned int     (__stdcall *FuncSetQHYCCDReadMode)(void*,unsigned int);
typedef unsigned int     (__stdcall *FuncGetQHYCCDReadMode)(void*,unsigned int *);



extern FuncInitQHYCCDResource              InitQHYCCDResource;
extern FuncReleaseQHYCCDResource           ReleaseQHYCCDResource;
extern FuncScanQHYCCD                      ScanQHYCCD;
extern FuncGetQHYCCDId                     GetQHYCCDId;
extern FuncOpenQHYCCD                      OpenQHYCCD;
extern FuncCloseQHYCCD                     CloseQHYCCD;
extern FuncInitQHYCCD                      InitQHYCCD;
extern FuncSetQHYCCDParam                  SetQHYCCDParam;
extern FuncSetQHYCCDBinMode                SetQHYCCDBinMode;
extern FuncGetQHYCCDMemLength              GetQHYCCDMemLength;
extern FuncExpQHYCCDSingleFrame            ExpQHYCCDSingleFrame;
extern FuncGetQHYCCDSingleFrame            GetQHYCCDSingleFrame;
extern FuncBeginQHYCCDLive                 BeginQHYCCDLive;
extern FuncGetQHYCCDLiveFrame              GetQHYCCDLiveFrame;
extern FuncCancelQHYCCDExposingAndReadout  CancelQHYCCDExposingAndReadout;
extern FuncSetQHYCCDStreamMode             SetQHYCCDStreamMode;
extern FuncSetQHYCCDResolution             SetQHYCCDResolution;
extern FuncStopQHYCCDLive                  StopQHYCCDLive;
extern FuncIsQHYCCDControlAvailable        IsQHYCCDControlAvailable;
extern FuncGetQHYCCDChipInfo               GetQHYCCDChipInfo;
extern FuncGetQHYCCDParam                  GetQHYCCDParam;
extern FuncQHYCCDVendRequestWrite          QHYCCDVendRequestWrite;
extern FuncQHYCCDVendRequestRead           QHYCCDVendRequestRead;
extern FuncQHYCCDReadUSB_SYNC              QHYCCDReadUSB_SYNC;

extern FuncGetQHYCCDNumberOfReadModes      GetQHYCCDNumberOfReadModes ;
extern FuncGetQHYCCDReadModeResolution     GetQHYCCDReadModeResolution ;
extern FuncGetQHYCCDReadModeName           GetQHYCCDReadModeName   ;
extern FuncSetQHYCCDReadMode               SetQHYCCDReadMode     ;
extern FuncGetQHYCCDReadMode               GetQHYCCDReadMode    ;
//--------------------------------------------------------------------------

void QHYCCDImport();

void QHYCCDExport();


#ifdef __cplusplus
}
#endif

//---------------------------------------------------------------------------
#endif
