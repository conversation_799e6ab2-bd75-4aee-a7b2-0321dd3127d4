#!/usr/bin/env python3
"""
Dependency Test Script
Test all required dependencies for QHYCCD application
"""

import sys

def test_imports():
    """Test all required imports"""
    print("Testing Python dependencies...")
    print("=" * 40)
    
    # Test basic Python modules
    try:
        import tkinter as tk
        print("✓ tkinter (GUI framework)")
    except ImportError as e:
        print(f"✗ tkinter: {e}")
        return False
    
    # Test numpy
    try:
        import numpy as np
        print(f"✓ numpy {np.__version__}")
    except ImportError as e:
        print(f"✗ numpy: {e}")
        return False
    
    # Test OpenCV
    try:
        import cv2
        print(f"✓ opencv-python {cv2.__version__}")
    except ImportError as e:
        print(f"✗ opencv-python: {e}")
        return False
    
    # Test PIL/Pillow
    try:
        from PIL import Image, ImageTk
        import PIL
        print(f"✓ Pillow {PIL.__version__}")
    except ImportError as e:
        print(f"✗ Pillow: {e}")
        return False
    
    # Test pyserial
    try:
        import serial
        import serial.tools.list_ports
        print(f"✓ pyserial {serial.__version__}")
        
        # Test COM port detection
        ports = serial.tools.list_ports.comports()
        print(f"  Available COM ports: {len(ports)}")
        for port in ports[:3]:  # Show first 3 ports
            print(f"    {port.device}: {port.description}")
            
    except ImportError as e:
        print(f"✗ pyserial: {e}")
        return False
    except Exception as e:
        print(f"⚠ pyserial installed but COM port detection failed: {e}")
    
    # Test matplotlib
    try:
        import matplotlib
        import matplotlib.pyplot as plt
        print(f"✓ matplotlib {matplotlib.__version__}")
    except ImportError as e:
        print(f"✗ matplotlib: {e}")
        return False
    
    # Test threading
    try:
        import threading
        import time
        print("✓ threading support")
    except ImportError as e:
        print(f"✗ threading: {e}")
        return False
    
    # Test ctypes (for DLL loading)
    try:
        import ctypes
        print("✓ ctypes (for DLL interface)")
    except ImportError as e:
        print(f"✗ ctypes: {e}")
        return False
    
    return True

def test_tkinter_gui():
    """Test basic tkinter GUI functionality"""
    print("\nTesting GUI functionality...")
    print("=" * 40)
    
    try:
        import tkinter as tk
        from tkinter import ttk
        
        # Create a test window
        root = tk.Tk()
        root.title("Dependency Test")
        root.geometry("300x200")
        root.withdraw()  # Hide the window
        
        # Test basic widgets
        frame = ttk.Frame(root)
        label = ttk.Label(frame, text="Test Label")
        button = ttk.Button(frame, text="Test Button")
        scale = ttk.Scale(frame, from_=0, to=100)
        
        print("✓ Basic tkinter widgets")
        
        # Test canvas (for image display)
        canvas = tk.Canvas(frame, width=100, height=100)
        print("✓ Canvas widget")
        
        # Clean up
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ GUI test failed: {e}")
        return False

def test_image_processing():
    """Test basic image processing functionality"""
    print("\nTesting image processing...")
    print("=" * 40)
    
    try:
        import numpy as np
        import cv2
        from PIL import Image, ImageTk
        
        # Create test image
        test_image = np.random.randint(0, 255, (100, 100), dtype=np.uint8)
        print("✓ NumPy array creation")
        
        # Test OpenCV operations
        resized = cv2.resize(test_image, (50, 50))
        print("✓ OpenCV resize")
        
        # Test PIL conversion
        pil_image = Image.fromarray(test_image)
        print("✓ PIL Image conversion")
        
        # Test PhotoImage (for tkinter display)
        photo = ImageTk.PhotoImage(pil_image)
        print("✓ ImageTk PhotoImage")
        
        return True
        
    except Exception as e:
        print(f"✗ Image processing test failed: {e}")
        return False

def test_file_access():
    """Test file system access"""
    print("\nTesting file system access...")
    print("=" * 40)
    
    try:
        import os
        
        # Check current directory
        current_dir = os.getcwd()
        print(f"✓ Current directory: {current_dir}")
        
        # Check if we can write files
        test_file = "test_write.tmp"
        try:
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            print("✓ File write/delete permissions")
        except Exception as e:
            print(f"⚠ File write test failed: {e}")
        
        # Check for QHYCCD DLL
        dll_paths = [
            "qhyccd.dll",
            "../delphi/qhyccd.dll",
            os.path.join(os.environ.get('SYSTEMROOT', ''), 'System32', 'qhyccd.dll'),
        ]
        
        dll_found = False
        for dll_path in dll_paths:
            if os.path.exists(dll_path):
                print(f"✓ QHYCCD DLL found: {dll_path}")
                dll_found = True
                break
        
        if not dll_found:
            print("⚠ QHYCCD DLL not found in common locations")
        
        return True
        
    except Exception as e:
        print(f"✗ File system test failed: {e}")
        return False

def main():
    """Main test function"""
    print("QHYCCD Application Dependency Test")
    print("=" * 50)
    print(f"Python version: {sys.version}")
    print(f"Platform: {sys.platform}")
    print()
    
    all_tests_passed = True
    
    # Run all tests
    tests = [
        ("Import Test", test_imports),
        ("GUI Test", test_tkinter_gui),
        ("Image Processing Test", test_image_processing),
        ("File System Test", test_file_access),
    ]
    
    for test_name, test_func in tests:
        try:
            if not test_func():
                all_tests_passed = False
        except Exception as e:
            print(f"✗ {test_name} crashed: {e}")
            all_tests_passed = False
        print()
    
    # Summary
    print("=" * 50)
    if all_tests_passed:
        print("✓ All tests passed! The application should work correctly.")
    else:
        print("✗ Some tests failed. Please check the errors above.")
        print("\nCommon solutions:")
        print("1. Install missing packages: pip install -r requirements.txt")
        print("2. Ensure QHYCCD SDK is installed")
        print("3. Check Python version (3.7+ required)")
    
    return 0 if all_tests_passed else 1

if __name__ == "__main__":
    sys.exit(main())
