// QCommon.h

#ifndef _QCOMMON_H_
#define _QCOMMON_H_

#ifdef __cplusplus 
extern "C" {
#endif

bool __cdecl QComIsCmos(int camType);
bool __cdecl QComGetFrameSize(int camType, unsigned char mode, unsigned char hBin, unsigned char vBin, int* pW, int* pH);
int __cdecl QComGetMaxFrameLength(int camType);
int __cdecl QComGetRawDataLength(int camType, unsigned char mode, unsigned char hBin, unsigned char vBin, int sfWidth, int sfHeight, unsigned char depth);
int __cdecl QComGetDemosaicMode(int camType, unsigned char mode, unsigned char hBin, unsigned char vBin);
unsigned short __cdecl QComGetHBlankingParam(int camType, int frameRateLvl);
double __cdecl QComGetExposeTimeParams(int camType, unsigned char mode, unsigned char hBin, unsigned char vBin, int sfWidth, int sfHeight, unsigned short hBlanking,
									 double exposeTime, unsigned short* paramLive, unsigned short* paramLong);
unsigned short __cdecl QComGetGainParam(int camType, unsigned short gain);
void __cdecl initStart(void);

#ifdef __cplusplus 
}
#endif

#endif
