"""
QHYCCD Sensor Performance Test Tools - Python GUI Implementation
Main application window and GUI components
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import logging
from typing import Optional, Dict, Any

# Import our modules
try:
    from camera_controller import <PERSON><PERSON><PERSON><PERSON>DController, ControlID
    from serial_controller import <PERSON>ial<PERSON><PERSON>roller
    from image_processor import ImageProcessor
    from test_automation import TestAutomation, TestType
    from utils import *
except ImportError as e:
    print(f"Error importing application modules: {e}")
    print("Please ensure all files are in the correct location")
    import sys
    sys.exit(1)

class QHYCCDTestApp:
    """Main application class"""
    
    def __init__(self):
        # Initialize logging
        logging.basicConfig(level=logging.INFO, 
                          format='%(asctime)s - %(levelname)s - %(message)s')
        
        # Initialize controllers
        self.camera = QHYCCDController()
        self.serial = SerialController()
        self.image_processor = ImageProcessor()
        self.test_automation = TestAutomation(self.camera, self.serial, self.image_processor)
        
        # GUI state
        self.is_live_preview = False
        self.preview_thread = None
        self.stop_preview = False
        
        # Current values
        self.current_gain = 0
        self.current_exposure = 45000
        self.current_offset = 30
        self.current_gain_r = 100
        self.current_gain_g = 100
        self.current_gain_b = 100
        
        # Register manipulation
        self.bit_manipulator = BitManipulator()
        
        # Create GUI
        self.create_gui()
        
        # Setup test automation callbacks
        self.test_automation.set_callbacks(
            progress_callback=self.on_test_progress,
            result_callback=self.on_test_result,
            image_callback=self.on_test_image
        )
    
    def create_gui(self):
        """Create main GUI window"""
        self.root = tk.Tk()
        self.root.title("QHYCCD Sensor Performance Test Tools")
        self.root.geometry("1453x870")  # Increased height from 845 to 870
        self.root.resizable(True, True)
        
        # Create main frames
        self.create_connection_frame()
        self.create_register_frame()
        self.create_parameter_frame()
        self.create_image_frame()
        self.create_test_frame()
        self.create_serial_frame()
        self.create_status_frame()
        
        # Bind close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_connection_frame(self):
        """Create camera connection controls"""
        frame = ttk.LabelFrame(self.root, text="Camera Connection", padding=10)
        frame.place(x=10, y=8, width=460, height=85)  # Increased height from 60 to 85

        # Read mode combo
        ttk.Label(frame, text="Read Mode:").place(x=0, y=0)
        self.combo_read_mode = ttk.Combobox(frame, width=15, state="readonly")
        self.combo_read_mode.place(x=80, y=0)

        # Total modes label
        self.label_total_modes = ttk.Label(frame, text="Total ReadModes: 0")
        self.label_total_modes.place(x=210, y=2)  # Slightly adjusted for better alignment

        # Connect button
        self.btn_connect = ttk.Button(frame, text="连接相机", command=self.connect_camera)
        self.btn_connect.place(x=340, y=0, width=105, height=28)  # Adjusted position and size

        # COM port selection
        ttk.Label(frame, text="COM Port:").place(x=0, y=35)  # Moved down from 25 to 35
        self.combo_com_port = ttk.Combobox(frame, width=10, values=self.get_com_ports())
        self.combo_com_port.set("COM3")
        self.combo_com_port.place(x=80, y=35)  # Moved down from 25 to 35

        # Serial connect button
        self.btn_serial_connect = ttk.Button(frame, text="打开串口连接", command=self.connect_serial)
        self.btn_serial_connect.place(x=200, y=35, width=120, height=25)  # Moved down and made wider
    
    def create_register_frame(self):
        """Create register read/write controls"""
        frame = ttk.LabelFrame(self.root, text="Register Operations", padding=10)
        frame.place(x=8, y=100, width=460, height=250)  # Moved down from y=72 to y=100
        
        # Register address input
        ttk.Label(frame, text="Register Address:").place(x=0, y=0)
        self.entry_reg_addr = ttk.Entry(frame, width=15, font=("Tahoma", 12))
        self.entry_reg_addr.insert(0, "0000")
        self.entry_reg_addr.place(x=0, y=20)
        
        # Register value input
        ttk.Label(frame, text="Register Value:").place(x=120, y=0)
        self.entry_reg_value = ttk.Entry(frame, width=15, font=("Tahoma", 12))
        self.entry_reg_value.insert(0, "0012")
        self.entry_reg_value.place(x=120, y=20)
        
        # Write/Read buttons
        ttk.Button(frame, text="WriteREG", command=self.write_register).place(x=240, y=20, width=80, height=30)
        ttk.Button(frame, text="ReadREG", command=self.read_register).place(x=330, y=20, width=80, height=30)
        
        # Bit manipulation
        ttk.Label(frame, text="Bit Manipulation:").place(x=0, y=60)
        self.entry_bit_reg = ttk.Entry(frame, width=15, font=("Tahoma", 12))
        self.entry_bit_reg.insert(0, "0000")
        self.entry_bit_reg.place(x=0, y=80)
        
        # Bit buttons (16 bits)
        self.bit_buttons = []
        for i in range(8):
            btn = ttk.Button(frame, text="0", width=3, 
                           command=lambda idx=i: self.toggle_bit(idx))
            btn.place(x=120 + i*25, y=80, width=20, height=20)
            self.bit_buttons.append(btn)
        
        for i in range(8):
            btn = ttk.Button(frame, text="0", width=3, 
                           command=lambda idx=i+8: self.toggle_bit(idx))
            btn.place(x=120 + i*25, y=105, width=20, height=20)
            self.bit_buttons.append(btn)
        
        ttk.Button(frame, text="WriteREG", command=self.write_bit_register).place(x=340, y=80, width=80, height=25)
        ttk.Button(frame, text="ReadREG", command=self.read_bit_register).place(x=340, y=110, width=80, height=25)
        
        # Scrollbars for parameter adjustment
        ttk.Label(frame, text="Parameter Adjustment:").place(x=0, y=140)
        
        self.scale_param1 = ttk.Scale(frame, from_=0, to=255, orient=tk.HORIZONTAL, 
                                     command=self.on_scale_change)
        self.scale_param1.place(x=120, y=140, width=200, height=25)
        
        self.scale_param2 = ttk.Scale(frame, from_=0, to=65535, orient=tk.HORIZONTAL,
                                     command=self.on_scale2_change)
        self.scale_param2.place(x=120, y=170, width=200, height=25)
        
        # Parameter display
        self.entry_param_display = ttk.Entry(frame, width=15, font=("Tahoma", 10))
        self.entry_param_display.insert(0, "0000")
        self.entry_param_display.place(x=0, y=170)
        
        # Memory test buttons
        ttk.Button(frame, text="内存测试", command=self.memory_test).place(x=340, y=140, width=80, height=30)
        ttk.Button(frame, text="单帧曝光", command=self.single_exposure).place(x=340, y=175, width=80, height=30)
    
    def create_parameter_frame(self):
        """Create camera parameter controls"""
        frame = ttk.LabelFrame(self.root, text="Camera Parameters", padding=10)
        frame.place(x=8, y=358, width=460, height=400)  # Moved down from y=330 to y=358
        
        # Gain control
        ttk.Label(frame, text="增益:").place(x=0, y=0)
        self.scale_gain = ttk.Scale(frame, from_=0, to=511, orient=tk.HORIZONTAL,
                                   command=self.on_gain_change)
        self.scale_gain.place(x=50, y=0, width=150, height=25)
        
        # Digital gain
        ttk.Label(frame, text="数字增益:").place(x=0, y=30)
        self.scale_digital_gain = ttk.Scale(frame, from_=0, to=1023, orient=tk.HORIZONTAL,
                                           command=self.on_digital_gain_change)
        self.scale_digital_gain.place(x=80, y=30, width=150, height=25)
        
        # Row noise control
        ttk.Label(frame, text="行噪声控制:").place(x=0, y=60)
        self.scale_row_noise = ttk.Scale(frame, from_=1, to=16383, orient=tk.HORIZONTAL,
                                        command=self.on_row_noise_change)
        self.scale_row_noise.set(1)
        self.scale_row_noise.place(x=80, y=60, width=150, height=25)
        
        # SVR control
        ttk.Label(frame, text="SVR噪声控制:").place(x=0, y=90)
        self.scale_svr = ttk.Scale(frame, from_=1, to=16383, orient=tk.HORIZONTAL,
                                  command=self.on_svr_change)
        self.scale_svr.set(1)
        self.scale_svr.place(x=100, y=90, width=150, height=25)
        
        # V blank
        ttk.Label(frame, text="V blank:").place(x=0, y=120)
        self.scale_vblank = ttk.Scale(frame, from_=0, to=63, orient=tk.HORIZONTAL,
                                     command=self.on_vblank_change)
        self.scale_vblank.place(x=60, y=120, width=150, height=25)
        
        # Crop controls
        ttk.Label(frame, text="Crop Start line:").place(x=0, y=150)
        self.scale_crop_start = ttk.Scale(frame, from_=1, to=16383, orient=tk.HORIZONTAL,
                                         command=self.on_crop_start_change)
        self.scale_crop_start.set(1)
        self.scale_crop_start.place(x=100, y=150, width=150, height=25)
        
        ttk.Label(frame, text="Crop Width:").place(x=0, y=180)
        self.scale_crop_width = ttk.Scale(frame, from_=0, to=16383, orient=tk.HORIZONTAL,
                                         command=self.on_crop_width_change)
        self.scale_crop_width.place(x=80, y=180, width=150, height=25)
        
        # Offset control
        ttk.Label(frame, text="输出OFFSET:").place(x=0, y=210)
        self.scale_offset = ttk.Scale(frame, from_=1, to=2047, orient=tk.HORIZONTAL,
                                     command=self.on_offset_change)
        self.scale_offset.set(1)
        self.scale_offset.place(x=100, y=210, width=150, height=25)
        
        # RGB gains
        ttk.Label(frame, text="R:").place(x=0, y=240)
        self.scale_gain_r = ttk.Scale(frame, from_=15, to=4095, orient=tk.HORIZONTAL,
                                     command=self.on_gain_r_change)
        self.scale_gain_r.set(15)
        self.scale_gain_r.place(x=20, y=240, width=150, height=25)
        
        ttk.Label(frame, text="G:").place(x=0, y=270)
        self.scale_gain_g = ttk.Scale(frame, from_=15, to=4095, orient=tk.HORIZONTAL,
                                     command=self.on_gain_g_change)
        self.scale_gain_g.set(15)
        self.scale_gain_g.place(x=20, y=270, width=150, height=25)
        
        ttk.Label(frame, text="B:").place(x=0, y=300)
        self.scale_gain_b = ttk.Scale(frame, from_=15, to=4095, orient=tk.HORIZONTAL,
                                     command=self.on_gain_b_change)
        self.scale_gain_b.set(15)
        self.scale_gain_b.place(x=20, y=300, width=150, height=25)
        
        # Additional controls
        ttk.Label(frame, text="black dummy clamp start line:").place(x=0, y=330)
        self.scale_clamp_start = ttk.Scale(frame, from_=0, to=1023, orient=tk.HORIZONTAL,
                                          command=self.on_clamp_start_change)
        self.scale_clamp_start.place(x=200, y=330, width=150, height=25)
        
        ttk.Label(frame, text="black dummy clamp number:").place(x=0, y=360)
        self.scale_clamp_number = ttk.Scale(frame, from_=0, to=2047, orient=tk.HORIZONTAL,
                                           command=self.on_clamp_number_change)
        self.scale_clamp_number.place(x=200, y=360, width=150, height=25)
    
    def create_image_frame(self):
        """Create image display area"""
        frame = ttk.LabelFrame(self.root, text="Image Display", padding=10)
        frame.place(x=478, y=8, width=720, height=420)
        
        # Preview image
        self.canvas_preview = tk.Canvas(frame, width=352, height=201, bg='black')
        self.canvas_preview.place(x=0, y=0)
        ttk.Label(frame, text="Preview").place(x=0, y=205)
        
        # Flat images
        self.canvas_flat1 = tk.Canvas(frame, width=352, height=201, bg='black')
        self.canvas_flat1.place(x=360, y=0)
        ttk.Label(frame, text="Flat A").place(x=360, y=205)
        
        self.canvas_flat2 = tk.Canvas(frame, width=352, height=201, bg='black')
        self.canvas_flat2.place(x=0, y=220)
        ttk.Label(frame, text="Flat B").place(x=0, y=425)
        
        # Bias images
        self.canvas_bias1 = tk.Canvas(frame, width=352, height=201, bg='black')
        self.canvas_bias1.place(x=360, y=220)
        ttk.Label(frame, text="Bias A").place(x=360, y=425)
        
        # Control buttons
        ttk.Button(frame, text="SHOWIMG", command=self.start_live_preview).place(x=10, y=450, width=80, height=30)
        ttk.Button(frame, text="STOPSHOW", command=self.stop_live_preview).place(x=100, y=450, width=80, height=30)
        ttk.Button(frame, text="高增益", command=self.high_gain_mode).place(x=190, y=450, width=80, height=30)
        ttk.Button(frame, text="输出噪声", command=self.output_noise).place(x=280, y=450, width=80, height=30)
    
    def create_test_frame(self):
        """Create test automation controls"""
        frame = ttk.LabelFrame(self.root, text="Test Automation", padding=10)
        frame.place(x=1200, y=8, width=240, height=500)
        
        # Live frame button
        ttk.Button(frame, text="LiveFrame", command=self.start_live_frame).place(x=0, y=0, width=100, height=40)
        
        # Exposure time adjustment
        ttk.Button(frame, text="ExpTimeAdj", command=self.start_exposure_adjustment).place(x=0, y=50, width=100, height=40)
        
        # Run test button
        ttk.Button(frame, text="Run Test", command=self.start_test).place(x=0, y=100, width=100, height=40)
        
        # Stop test button
        ttk.Button(frame, text="Stop Test", command=self.stop_test).place(x=110, y=100, width=100, height=40)
        
        # Gain control
        ttk.Button(frame, text="Gain", command=self.gain_control).place(x=0, y=150, width=100, height=25)
        
        # Gain percentage buttons
        gain_percentages = ["0%", "25%", "50%", "75%", "100%"]
        for i, pct in enumerate(gain_percentages):
            ttk.Button(frame, text=pct, width=8,
                      command=lambda p=pct: self.set_gain_percentage(p)).place(x=i*40, y=180, width=35, height=25)
        
        # Exposure time buttons (ms)
        exp_times = ["1", "2", "5", "7", "10", "15", "20", "25", "30", "40", "50", "100", "200", "500"]
        for i, exp in enumerate(exp_times):
            row = i // 5
            col = i % 5
            ttk.Button(frame, text=exp, width=6,
                      command=lambda e=exp: self.set_exposure_time(int(e))).place(
                          x=col*40, y=210 + row*30, width=35, height=25)
        
        # Gain start/step controls
        ttk.Label(frame, text="开始增益:").place(x=0, y=320)
        self.entry_gain_start = ttk.Entry(frame, width=8)
        self.entry_gain_start.insert(0, "0")
        self.entry_gain_start.place(x=60, y=320)
        
        ttk.Label(frame, text="增益间隔:").place(x=130, y=320)
        self.entry_gain_step = ttk.Entry(frame, width=8)
        self.entry_gain_step.insert(0, "5")
        self.entry_gain_step.place(x=180, y=320)
        
        # Gain sliders
        ttk.Label(frame, text="gain:").place(x=0, y=350)
        self.scale_main_gain = ttk.Scale(frame, from_=0, to=255, orient=tk.HORIZONTAL,
                                        command=self.on_main_gain_change)
        self.scale_main_gain.place(x=40, y=350, width=180, height=20)
        
        ttk.Label(frame, text="gainR:").place(x=0, y=375)
        self.scale_test_gain_r = ttk.Scale(frame, from_=0, to=255, orient=tk.HORIZONTAL,
                                          command=self.on_test_gain_r_change)
        self.scale_test_gain_r.set(128)
        self.scale_test_gain_r.place(x=40, y=375, width=180, height=20)
        
        ttk.Label(frame, text="gainG:").place(x=0, y=400)
        self.scale_test_gain_g = ttk.Scale(frame, from_=0, to=255, orient=tk.HORIZONTAL,
                                          command=self.on_test_gain_g_change)
        self.scale_test_gain_g.set(128)
        self.scale_test_gain_g.place(x=40, y=400, width=180, height=20)
        
        ttk.Label(frame, text="gainB:").place(x=0, y=425)
        self.scale_test_gain_b = ttk.Scale(frame, from_=0, to=255, orient=tk.HORIZONTAL,
                                          command=self.on_test_gain_b_change)
        self.scale_test_gain_b.set(128)
        self.scale_test_gain_b.place(x=40, y=425, width=180, height=20)
    
    def create_serial_frame(self):
        """Create serial port controls"""
        frame = ttk.LabelFrame(self.root, text="Serial Control", padding=10)
        frame.place(x=1200, y=520, width=240, height=100)
        
        # Serial port buttons
        ttk.Button(frame, text="Comport Open", command=self.open_comport).place(x=0, y=0, width=75, height=25)
        ttk.Button(frame, text="OFF", command=self.flat_panel_off).place(x=80, y=0, width=75, height=25)
        ttk.Button(frame, text="ON", command=self.flat_panel_on).place(x=160, y=0, width=75, height=25)
        
        ttk.Button(frame, text="Clear Memo", command=self.clear_memo).place(x=0, y=30, width=75, height=25)
    
    def create_status_frame(self):
        """Create status and memo area"""
        frame = ttk.LabelFrame(self.root, text="Status & Log", padding=10)
        frame.place(x=1200, y=630, width=240, height=200)
        
        # Memo/log area
        self.text_memo = tk.Text(frame, width=28, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.text_memo.yview)
        self.text_memo.configure(yscrollcommand=scrollbar.set)
        
        self.text_memo.place(x=0, y=0, width=200, height=160)
        scrollbar.place(x=205, y=0, width=20, height=160)
        
        # Add initial message
        self.add_memo("QHYCCD Test Application Started")
    
    # ==================== Event Handlers ====================

    def connect_camera(self):
        """Connect to QHYCCD camera"""
        try:
            if not self.camera.is_connected:
                # Initialize resources
                if not self.camera.initialize_resources():
                    messagebox.showerror("Error", "Failed to initialize QHYCCD resources")
                    return

                # Connect to camera
                if self.camera.connect_camera():
                    self.btn_connect.config(text=f"Connected: {self.camera.camera_id}")
                    self.add_memo(f"Connected to camera: {self.camera.camera_id}")

                    # Update read modes
                    self.combo_read_mode['values'] = self.camera.read_modes
                    if self.camera.read_modes:
                        self.combo_read_mode.current(0)
                        self.camera.set_read_mode(0)

                    self.label_total_modes.config(text=f"Total ReadModes: {len(self.camera.read_modes)}")

                    # Initialize camera
                    self.camera.init_camera()
                    self.camera.set_resolution(0, 0, 1000, 1000)

                    # Set default parameters
                    self.setup_default_camera_params()

                else:
                    messagebox.showerror("Error", "Failed to connect to camera")
            else:
                # Disconnect
                if self.camera.disconnect_camera():
                    self.btn_connect.config(text="连接相机")
                    self.add_memo("Camera disconnected")
                    self.combo_read_mode['values'] = []
                    self.label_total_modes.config(text="Total ReadModes: 0")

        except Exception as e:
            messagebox.showerror("Error", f"Camera connection error: {e}")
            logging.error(f"Camera connection error: {e}")

    def connect_serial(self):
        """Connect to serial port"""
        try:
            port_name = self.combo_com_port.get()
            if not self.serial.is_connected:
                self.serial.set_port_settings(port_name)
                if self.serial.connect():
                    self.btn_serial_connect.config(text="关闭串口")
                    self.add_memo(f"Serial port {port_name} connected")
                else:
                    messagebox.showerror("Error", f"Failed to connect to {port_name}")
            else:
                if self.serial.disconnect():
                    self.btn_serial_connect.config(text="打开串口连接")
                    self.add_memo("Serial port disconnected")

        except Exception as e:
            messagebox.showerror("Error", f"Serial connection error: {e}")
            logging.error(f"Serial connection error: {e}")

    def write_register(self):
        """Write to camera register"""
        try:
            addr_str = self.entry_reg_addr.get()
            value_str = self.entry_reg_value.get()

            addr = hex_to_int(addr_str)
            value = hex_to_int(value_str)

            if self.camera.write_register(addr, value):
                self.add_memo(f"Write REG[{format_register_value(addr)}] = {format_register_value(value)}")
            else:
                messagebox.showerror("Error", "Failed to write register")

        except Exception as e:
            messagebox.showerror("Error", f"Register write error: {e}")

    def read_register(self):
        """Read from camera register"""
        try:
            addr_str = self.entry_reg_addr.get()
            addr = hex_to_int(addr_str)

            value = self.camera.read_register(addr)
            self.entry_reg_value.delete(0, tk.END)
            self.entry_reg_value.insert(0, int_to_hex(value))

            self.add_memo(f"Read REG[{format_register_value(addr)}] = {format_register_value(value)}")

        except Exception as e:
            messagebox.showerror("Error", f"Register read error: {e}")

    def toggle_bit(self, bit_index):
        """Toggle bit in bit manipulator"""
        current_value = self.bit_manipulator.get_bit(bit_index)
        new_value = not current_value
        self.bit_manipulator.set_bit(bit_index, new_value)

        # Update button text
        self.bit_buttons[bit_index].config(text="1" if new_value else "0")

        # Update register value display
        reg_value = self.bit_manipulator.to_int()
        self.entry_bit_reg.delete(0, tk.END)
        self.entry_bit_reg.insert(0, int_to_hex(reg_value))

    def write_bit_register(self):
        """Write bit register to camera"""
        try:
            reg_value = self.bit_manipulator.to_int()
            addr_str = self.entry_reg_addr.get()
            addr = hex_to_int(addr_str)

            if self.camera.write_register(addr, reg_value):
                self.add_memo(f"Write Bit REG[{format_register_value(addr)}] = {format_register_value(reg_value)}")
            else:
                messagebox.showerror("Error", "Failed to write bit register")

        except Exception as e:
            messagebox.showerror("Error", f"Bit register write error: {e}")

    def read_bit_register(self):
        """Read bit register from camera"""
        try:
            addr_str = self.entry_reg_addr.get()
            addr = hex_to_int(addr_str)

            value = self.camera.read_register(addr)
            self.bit_manipulator.from_int(value)

            # Update bit buttons
            for i in range(16):
                bit_value = self.bit_manipulator.get_bit(i)
                self.bit_buttons[i].config(text="1" if bit_value else "0")

            # Update register display
            self.entry_bit_reg.delete(0, tk.END)
            self.entry_bit_reg.insert(0, int_to_hex(value))

            self.add_memo(f"Read Bit REG[{format_register_value(addr)}] = {format_register_value(value)}")

        except Exception as e:
            messagebox.showerror("Error", f"Bit register read error: {e}")

    def on_scale_change(self, value):
        """Handle scale 1 change"""
        int_value = int(float(value))
        self.entry_param_display.delete(0, tk.END)
        self.entry_param_display.insert(0, int_to_hex(int_value))

    def on_scale2_change(self, value):
        """Handle scale 2 change"""
        int_value = int(float(value))
        # Update some parameter based on scale 2
        pass

    def memory_test(self):
        """Perform memory test"""
        self.add_memo("Memory test started...")
        # Implement memory test logic
        pass

    def single_exposure(self):
        """Perform single exposure"""
        try:
            if self.camera.is_connected:
                # Capture single frame
                frame = self.camera.get_live_frame()
                if frame is not None:
                    self.display_image(frame, self.canvas_preview)
                    self.add_memo("Single exposure captured")
                else:
                    self.add_memo("Failed to capture single exposure")
            else:
                messagebox.showwarning("Warning", "Camera not connected")

        except Exception as e:
            messagebox.showerror("Error", f"Single exposure error: {e}")

    # ==================== Parameter Control Handlers ====================

    def on_gain_change(self, value):
        """Handle gain change"""
        self.current_gain = int(float(value))
        if self.camera.is_connected:
            self.camera.set_parameter(ControlID.CONTROL_GAIN, self.current_gain)

    def on_digital_gain_change(self, value):
        """Handle digital gain change"""
        digital_gain = int(float(value))
        # Set digital gain parameter
        pass

    def on_row_noise_change(self, value):
        """Handle row noise control change"""
        row_noise = int(float(value))
        # Set row noise parameter
        pass

    def on_svr_change(self, value):
        """Handle SVR control change"""
        svr_value = int(float(value))
        # Set SVR parameter
        pass

    def on_vblank_change(self, value):
        """Handle V blank change"""
        vblank = int(float(value))
        # Set V blank parameter
        pass

    def on_crop_start_change(self, value):
        """Handle crop start change"""
        crop_start = int(float(value))
        # Set crop start parameter
        pass

    def on_crop_width_change(self, value):
        """Handle crop width change"""
        crop_width = int(float(value))
        # Set crop width parameter
        pass

    def on_offset_change(self, value):
        """Handle offset change"""
        self.current_offset = int(float(value))
        if self.camera.is_connected:
            self.camera.set_parameter(ControlID.CONTROL_OFFSET, self.current_offset)

    def on_gain_r_change(self, value):
        """Handle R gain change"""
        self.current_gain_r = int(float(value))
        if self.camera.is_connected:
            self.camera.set_parameter(ControlID.CONTROL_WBR, self.current_gain_r)

    def on_gain_g_change(self, value):
        """Handle G gain change"""
        self.current_gain_g = int(float(value))
        if self.camera.is_connected:
            self.camera.set_parameter(ControlID.CONTROL_WBG, self.current_gain_g)

    def on_gain_b_change(self, value):
        """Handle B gain change"""
        self.current_gain_b = int(float(value))
        if self.camera.is_connected:
            self.camera.set_parameter(ControlID.CONTROL_WBB, self.current_gain_b)

    def on_clamp_start_change(self, value):
        """Handle clamp start change"""
        clamp_start = int(float(value))
        # Set clamp start parameter
        pass

    def on_clamp_number_change(self, value):
        """Handle clamp number change"""
        clamp_number = int(float(value))
        # Set clamp number parameter
        pass

    # ==================== Image Display and Live Preview ====================

    def start_live_preview(self):
        """Start live preview"""
        try:
            if not self.camera.is_connected:
                messagebox.showwarning("Warning", "Camera not connected")
                return

            if not self.is_live_preview:
                if self.camera.start_live():
                    self.is_live_preview = True
                    self.stop_preview = False
                    self.preview_thread = threading.Thread(target=self.live_preview_worker)
                    self.preview_thread.daemon = True
                    self.preview_thread.start()
                    self.add_memo("Live preview started")
                else:
                    messagebox.showerror("Error", "Failed to start live preview")

        except Exception as e:
            messagebox.showerror("Error", f"Live preview error: {e}")

    def stop_live_preview(self):
        """Stop live preview"""
        try:
            if self.is_live_preview:
                self.stop_preview = True
                if self.preview_thread and self.preview_thread.is_alive():
                    self.preview_thread.join(timeout=2.0)

                self.camera.stop_live()
                self.is_live_preview = False
                self.add_memo("Live preview stopped")

        except Exception as e:
            messagebox.showerror("Error", f"Stop preview error: {e}")

    def live_preview_worker(self):
        """Live preview worker thread"""
        while not self.stop_preview and self.is_live_preview:
            try:
                frame = self.camera.get_live_frame()
                if frame is not None:
                    # Update preview display in main thread
                    self.root.after(0, lambda: self.display_image(frame, self.canvas_preview))

                time.sleep(0.05)  # 20 FPS

            except Exception as e:
                logging.error(f"Live preview worker error: {e}")
                break

    def display_image(self, image_data, canvas):
        """Display image on canvas"""
        try:
            if image_data is None:
                return

            # Convert to displayable format
            tk_image = self.image_processor.create_tkinter_image(image_data, (352, 201))
            if tk_image:
                canvas.delete("all")
                canvas.create_image(176, 100, image=tk_image)
                # Keep reference to prevent garbage collection
                canvas.image = tk_image

        except Exception as e:
            logging.error(f"Display image error: {e}")

    def high_gain_mode(self):
        """Enable high gain mode"""
        try:
            if self.camera.is_connected:
                # Set high gain parameters
                self.camera.set_parameter(ControlID.CONTROL_GAIN, 200)
                self.scale_gain.set(200)
                self.add_memo("High gain mode enabled")
            else:
                messagebox.showwarning("Warning", "Camera not connected")

        except Exception as e:
            messagebox.showerror("Error", f"High gain mode error: {e}")

    def output_noise(self):
        """Output noise analysis"""
        try:
            if self.camera.is_connected and self.is_live_preview:
                frame = self.camera.get_live_frame()
                if frame is not None:
                    stats = self.image_processor.calculate_statistics(frame)
                    noise_info = f"Noise Analysis:\nMean: {stats['mean']:.2f}\nStd: {stats['std']:.2f}\nRMS: {stats['rms']:.2f}"
                    messagebox.showinfo("Noise Analysis", noise_info)
                    self.add_memo(f"Noise - Mean: {stats['mean']:.1f}, Std: {stats['std']:.1f}")
                else:
                    messagebox.showwarning("Warning", "No image data available")
            else:
                messagebox.showwarning("Warning", "Camera not connected or live preview not running")

        except Exception as e:
            messagebox.showerror("Error", f"Noise analysis error: {e}")

    # ==================== Test Automation Handlers ====================

    def start_live_frame(self):
        """Start live frame mode"""
        self.start_live_preview()

    def start_exposure_adjustment(self):
        """Start automatic exposure time adjustment"""
        try:
            if not self.camera.is_connected:
                messagebox.showwarning("Warning", "Camera not connected")
                return

            params = {
                'flat_exposure_time': self.current_exposure,
                'target_rms': 30000
            }

            if self.test_automation.start_test(TestType.EXPOSURE_ADJUSTMENT, params):
                self.add_memo("Exposure time adjustment started")
            else:
                messagebox.showerror("Error", "Failed to start exposure adjustment")

        except Exception as e:
            messagebox.showerror("Error", f"Exposure adjustment error: {e}")

    def start_test(self):
        """Start automated test"""
        try:
            if not self.camera.is_connected:
                messagebox.showwarning("Warning", "Camera not connected")
                return

            # Get test parameters
            params = {
                'gain_start': int(self.entry_gain_start.get()),
                'gain_step': int(self.entry_gain_step.get()),
                'flat_exposure_time': self.current_exposure
            }

            if self.test_automation.start_test(TestType.FLAT_BIAS_TEST, params):
                self.add_memo("Automated test started")
            else:
                messagebox.showerror("Error", "Failed to start test")

        except Exception as e:
            messagebox.showerror("Error", f"Test start error: {e}")

    def stop_test(self):
        """Stop current test"""
        try:
            self.test_automation.stop_current_test()
            self.add_memo("Test stopped")

        except Exception as e:
            messagebox.showerror("Error", f"Test stop error: {e}")

    def gain_control(self):
        """Open gain control dialog"""
        # Create gain control window
        gain_window = tk.Toplevel(self.root)
        gain_window.title("Gain Control")
        gain_window.geometry("400x300")

        # Add gain controls
        ttk.Label(gain_window, text="Main Gain:").pack(pady=5)
        gain_scale = ttk.Scale(gain_window, from_=0, to=255, orient=tk.HORIZONTAL,
                              command=lambda v: self.camera.set_parameter(ControlID.CONTROL_GAIN, int(float(v))))
        gain_scale.pack(fill=tk.X, padx=20, pady=5)
        gain_scale.set(self.current_gain)

    def set_gain_percentage(self, percentage):
        """Set gain by percentage"""
        try:
            pct_value = int(percentage.rstrip('%'))
            gain_value = int(255 * pct_value / 100)

            if self.camera.is_connected:
                self.camera.set_parameter(ControlID.CONTROL_GAIN, gain_value)
                self.scale_main_gain.set(gain_value)
                self.current_gain = gain_value
                self.add_memo(f"Gain set to {pct_value}% ({gain_value})")

        except Exception as e:
            messagebox.showerror("Error", f"Set gain percentage error: {e}")

    def set_exposure_time(self, time_ms):
        """Set exposure time in milliseconds"""
        try:
            exposure_us = time_ms * 1000  # Convert to microseconds

            if self.camera.is_connected:
                self.camera.set_parameter(ControlID.CONTROL_EXPOSURE, exposure_us)
                self.current_exposure = exposure_us
                self.add_memo(f"Exposure time set to {time_ms}ms")

        except Exception as e:
            messagebox.showerror("Error", f"Set exposure time error: {e}")

    def on_main_gain_change(self, value):
        """Handle main gain slider change"""
        gain_value = int(float(value))
        if self.camera.is_connected:
            self.camera.set_parameter(ControlID.CONTROL_GAIN, gain_value)
            self.current_gain = gain_value

    def on_test_gain_r_change(self, value):
        """Handle test R gain change"""
        gain_r = int(float(value))
        if self.camera.is_connected:
            self.camera.set_parameter(ControlID.CONTROL_WBR, gain_r)

    def on_test_gain_g_change(self, value):
        """Handle test G gain change"""
        gain_g = int(float(value))
        if self.camera.is_connected:
            self.camera.set_parameter(ControlID.CONTROL_WBG, gain_g)

    def on_test_gain_b_change(self, value):
        """Handle test B gain change"""
        gain_b = int(float(value))
        if self.camera.is_connected:
            self.camera.set_parameter(ControlID.CONTROL_WBB, gain_b)

    # ==================== Serial Control Handlers ====================

    def open_comport(self):
        """Open COM port"""
        self.connect_serial()

    def flat_panel_off(self):
        """Turn off flat panel"""
        try:
            if self.serial.is_connected:
                if self.serial.send_flat_panel_command('N'):
                    self.add_memo("Flat panel OFF")
                else:
                    messagebox.showerror("Error", "Failed to send OFF command")
            else:
                messagebox.showwarning("Warning", "Serial port not connected")

        except Exception as e:
            messagebox.showerror("Error", f"Flat panel OFF error: {e}")

    def flat_panel_on(self):
        """Turn on flat panel"""
        try:
            if self.serial.is_connected:
                if self.serial.send_flat_panel_command('F'):
                    self.add_memo("Flat panel ON")
                else:
                    messagebox.showerror("Error", "Failed to send ON command")
            else:
                messagebox.showwarning("Warning", "Serial port not connected")

        except Exception as e:
            messagebox.showerror("Error", f"Flat panel ON error: {e}")

    def clear_memo(self):
        """Clear memo text"""
        self.text_memo.delete(1.0, tk.END)

    # ==================== Test Automation Callbacks ====================

    def on_test_progress(self, message, progress):
        """Handle test progress updates"""
        self.add_memo(f"Test: {message} ({progress}%)")

    def on_test_result(self, results):
        """Handle test results"""
        self.add_memo("Test completed with results:")
        for key, value in results.items():
            self.add_memo(f"  {key}: {value}")

    def on_test_image(self, image_type, image_data, frame_index):
        """Handle test image updates"""
        try:
            if image_type == 'flat':
                if frame_index < 2:  # Display first two flat frames
                    canvas = self.canvas_flat1 if frame_index == 0 else self.canvas_flat2
                    self.display_image(image_data, canvas)
            elif image_type == 'bias':
                if frame_index < 2:  # Display first two bias frames
                    canvas = self.canvas_bias1 if frame_index == 0 else self.canvas_flat2  # Note: using flat2 for second bias
                    self.display_image(image_data, canvas)

        except Exception as e:
            logging.error(f"Test image display error: {e}")

    # ==================== Utility Methods ====================

    def setup_default_camera_params(self):
        """Setup default camera parameters"""
        try:
            if not self.camera.is_connected:
                return

            # Set default parameters
            self.camera.set_parameter(ControlID.CONTROL_DDR, 0)
            self.camera.set_parameter(ControlID.CONTROL_OFFSET, 30)
            self.camera.set_parameter(ControlID.CONTROL_USBTRAFFIC, 0)
            self.camera.set_parameter(ControlID.CONTROL_TRANSFERBIT, 16)
            self.camera.set_parameter(ControlID.CONTROL_EXPOSURE, 45000)
            self.camera.set_parameter(ControlID.CONTROL_GAIN, 0)
            self.camera.set_parameter(ControlID.CONTROL_WBR, 64)
            self.camera.set_parameter(ControlID.CONTROL_WBG, 64)
            self.camera.set_parameter(ControlID.CONTROL_WBB, 64)

            # Update GUI controls
            self.scale_offset.set(30)
            self.scale_main_gain.set(0)
            self.scale_test_gain_r.set(64)
            self.scale_test_gain_g.set(64)
            self.scale_test_gain_b.set(64)

            self.add_memo("Default camera parameters set")

        except Exception as e:
            logging.error(f"Setup default params error: {e}")

    def get_com_ports(self):
        """Get available COM ports"""
        try:
            return get_available_com_ports()
        except:
            return ["COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9"]

    def add_memo(self, message):
        """Add message to memo area"""
        try:
            timestamp = time.strftime("%H:%M:%S")
            full_message = f"[{timestamp}] {message}\n"

            self.text_memo.insert(tk.END, full_message)
            self.text_memo.see(tk.END)  # Scroll to bottom

            # Limit memo size
            lines = self.text_memo.get(1.0, tk.END).split('\n')
            if len(lines) > 1000:  # Keep last 1000 lines
                self.text_memo.delete(1.0, f"{len(lines)-1000}.0")

        except Exception as e:
            logging.error(f"Add memo error: {e}")

    def on_closing(self):
        """Handle application closing"""
        try:
            # Stop live preview
            if self.is_live_preview:
                self.stop_live_preview()

            # Stop any running tests
            if self.test_automation.is_test_running():
                self.test_automation.stop_current_test()

            # Disconnect camera
            if self.camera.is_connected:
                self.camera.disconnect_camera()

            # Disconnect serial
            if self.serial.is_connected:
                self.serial.disconnect()

            # Close application
            self.root.destroy()

        except Exception as e:
            logging.error(f"Closing error: {e}")
            self.root.destroy()

    def run(self):
        """Start the GUI application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = QHYCCDTestApp()
    app.run()
