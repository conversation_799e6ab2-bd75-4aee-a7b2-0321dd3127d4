/*
 QHYCCD SDK

 Copyright (c) 2014 QHYCCD.
 All Rights Reserved.

 This program is free software; you can redistribute it and/or modify it
 under the terms of the GNU General Public License as published by the Free
 Software Foundation; either version 2 of the License, or (at your option)
 any later version.

 This program is distributed in the hope that it will be useful, but WITHOUT
 ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License for
 more details.

 You should have received a copy of the GNU General Public License along with
 this program; if not, write to the Free Software Foundation, Inc., 59
 Temple Place - Suite 330, Boston, MA  02111-1307, USA.

 The full GNU General Public License is included in this distribution in the
 file called LICENSE.
 */

/*!
   @file qhyccdcamdef.h
   @brief QHYCCD SDK error define
  */

#ifndef __QHYCCDCAMDEF_H__
#define __QHYCCDCAMDEF_H__

//#define GIGAESUPPORT

/**
 * Type define for IMG0S */
#define DEVICETYPE_IMG0S        00

/**
 * Type define for IMG0H */
#define DEVICETYPE_IMG0H        01

/**
 * Type define for IMG0L */
#define DEVICETYPE_IMG0L        02

/**
 * Type define for IMG0X */
#define DEVICETYPE_IMG0X        03

/**
 * Type define for IMG1S */
#define DEVICETYPE_IMG1S        10

/**
 * Type define for QHY16 */
#define DEVICETYPE_QHY16        16

/**
 * Type define for IMG2S */
#define DEVICETYPE_IMG2S        20


/**
 * Type define for QHY5LII_COOL */
#define DEVICETYPE_QHY5LII_COOL 58

/**
 * Type define for QHY6 */
#define DEVICETYPE_QHY6         60

/**
 * Type define for QHY7 */
#define DEVICETYPE_QHY7         70

/**
 * Type define for IMG1E */
#define DEVICETYPE_IMG1E        110

/**
 * Type define for QHY2PRO */
#define DEVICETYPE_QHY2PRO      221

/**
 * Type define for IMG2P */
#define DEVICETYPE_IMG2P        220

/**
 * Type define for QHY8 */
#define DEVICETYPE_QHY8         400

/**
 * Type define for QHY8PRO */
#define DEVICETYPE_QHY8PRO      453

/**
 * Type define for QHY5II */
#define DEVICETYPE_QHY5II       350

/**
 * Type define for QHY5LII_M */
#define DEVICETYPE_QHY5LII_M    355

/**
 * Type define for QHY5TII */
#define DEVICETYPE_QHY5TII      351

/**
 * Type define for QHY5RII */
#define DEVICETYPE_QHY5RII      400

/**
 * Type define for QHY5PII */
#define DEVICETYPE_QHY5PII      354

/**
 * Type define for QHY5VII */
#define DEVICETYPE_QHY5VII      352

/**
 * Type define for QHY5HII */
#define DEVICETYPE_QHY5HII      358

/**
 * Type define for QHYXXX */
#define DEVICETYPE_MINICAM5S_M  359

/**
 * Type define for QHYXXX */
#define DEVICETYPE_MINICAM5S_C  360

/**
 * Type define for QHY16000 */
#define DEVICETYPE_QHY16000     361

/**
 * Type define for QHY5 */
#define DEVICETYPE_QHY5         364

/**
 * Type define for QHY5LII_C */
#define DEVICETYPE_QHY5LII_C    365

/**
 * Type define for QHY5PII_C */
#define DEVICETYPE_QHY5PII_C    366

/**
 * Type define for QHY12 */
#define DEVICETYPE_QHY12        613

/**
 * Type define for IC8300 */
#define DEVICETYPE_IC8300       890

/**
 * Type define for QHY9S */
#define DEVICETYPE_QHY9S        892

/**
 * Type define for QHY10 */
#define DEVICETYPE_QHY10        893

/**
 * Type define for QHY8L */
#define DEVICETYPE_QHY8L        891

/**
 * Type define for QHY11 */
#define DEVICETYPE_QHY11        894

/**
 * Type define for QHY21 */
#define DEVICETYPE_QHY21        895

/**
 * Type define for QHY22 */
#define DEVICETYPE_QHY22        896

/**
 * Type define for QHY23 */
#define DEVICETYPE_QHY23        897

/**
 * Type define for QHY15 */
#define DEVICETYPE_QHY15        898

/**
 * Type define for QHY27 */
#define DEVICETYPE_QHY27        899

/**
 * Type define for IC90A */
#define DEVICETYPE_IC90A        900


/**
 * Type define for IC16200A */
#define DEVICETYPE_IC16200A     901

/**
 * Type define for QHY28 */
#define DEVICETYPE_QHY28        902

/**
 * Type define for QHY5RII-M */
#define DEVICETYPE_QHY5RII_M    903

/**
 * Type define for QHY5RII-M */
#define DEVICETYPE_MINICAM5F_M  904

/**
 * Type define for QHY9T */
#define DEVICETYPE_QHY9T        905

/**
 * Type define for QHY9T */
#define DEVICETYPE_IC16803      906

/**
 * Type define for QHY29 */
#define DEVICETYPE_QHY29        907

/**
 * Type define for SOLAR1600 */
#define DEVICETYPE_SOLAR1600    908

/**
 * Type define for QHY5PII_M */
#define DEVICETYPE_QHY5PII_M    909

/**
 * Type define for Titan034 */
#define DEVICETYPE_TITAN034     910

/**
 * Type define for QHY5TII */
#define DEVICETYPE_QHY5TII_C    911

/**
 * Type define for POLEMASTER */
#define DEVICETYPE_POLEMASTER   912

/**
 * Type define for QHY15GIGAE */
#define DEVICETYPE_QHY15G       9000

/**
 * Type define for SOLAR800G */
#define DEVICETYPE_SOLAR800G    9001

#define DEVICETYPE_A0340G       9003

#define DEVICETYPE_QHY08050G    9004

#define DEVICETYPE_QHY694G      9005

#define DEVICETYPE_QHY27G       9006

#define DEVICETYPE_QHY23G       9007


/**
 * Type define for UNKNOW */
#define DEVICETYPE_UNKNOW       -1

#endif