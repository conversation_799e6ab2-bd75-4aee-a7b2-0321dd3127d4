"""
Serial Port Controller for QHYCCD application
Handles communication with external devices like flat panels
"""

import serial
import serial.tools.list_ports
import threading
import time
import logging
from typing import List, Optional

class SerialController:
    """Serial port communication controller"""
    
    def __init__(self):
        self.port = None
        self.is_connected = False
        self.port_name = "COM3"
        self.baud_rate = 9600
        self.data_bits = 8
        self.stop_bits = 1
        self.parity = serial.PARITY_NONE
        self.timeout = 1.0
        self.write_timeout = 1.0
        
        # Threading
        self.read_thread = None
        self.stop_reading = False
        self.read_callback = None
        
    def get_available_ports(self) -> List[str]:
        """Get list of available COM ports"""
        try:
            ports = serial.tools.list_ports.comports()
            return [port.device for port in ports]
        except Exception as e:
            logging.error(f"Failed to get COM ports: {e}")
            return []
    
    def set_port_settings(self, port_name: str, baud_rate: int = 9600, 
                         data_bits: int = 8, stop_bits: int = 1, 
                         parity: str = 'N', timeout: float = 1.0):
        """Set serial port settings"""
        self.port_name = port_name
        self.baud_rate = baud_rate
        self.data_bits = data_bits
        self.stop_bits = stop_bits
        self.timeout = timeout
        self.write_timeout = timeout
        
        # Convert parity string to serial constant
        parity_map = {
            'N': serial.PARITY_NONE,
            'E': serial.PARITY_EVEN,
            'O': serial.PARITY_ODD,
            'M': serial.PARITY_MARK,
            'S': serial.PARITY_SPACE
        }
        self.parity = parity_map.get(parity.upper(), serial.PARITY_NONE)
    
    def connect(self) -> bool:
        """Connect to serial port"""
        try:
            if self.is_connected:
                self.disconnect()
            
            self.port = serial.Serial(
                port=self.port_name,
                baudrate=self.baud_rate,
                bytesize=self.data_bits,
                stopbits=self.stop_bits,
                parity=self.parity,
                timeout=self.timeout,
                write_timeout=self.write_timeout
            )
            
            if self.port.is_open:
                self.is_connected = True
                logging.info(f"Connected to {self.port_name}")
                return True
            else:
                logging.error(f"Failed to open {self.port_name}")
                return False
                
        except Exception as e:
            logging.error(f"Failed to connect to {self.port_name}: {e}")
            return False
    
    def disconnect(self) -> bool:
        """Disconnect from serial port"""
        try:
            if self.read_thread and self.read_thread.is_alive():
                self.stop_reading = True
                self.read_thread.join(timeout=2.0)
            
            if self.port and self.port.is_open:
                self.port.close()
                self.is_connected = False
                logging.info(f"Disconnected from {self.port_name}")
                return True
            return True
            
        except Exception as e:
            logging.error(f"Failed to disconnect from {self.port_name}: {e}")
            return False
    
    def write_char(self, char: str) -> bool:
        """Write single character to serial port"""
        if not self.is_connected or not self.port:
            return False
        
        try:
            if isinstance(char, str):
                char = char.encode('utf-8')
            elif isinstance(char, int):
                char = bytes([char])
            
            bytes_written = self.port.write(char)
            self.port.flush()
            return bytes_written > 0
            
        except Exception as e:
            logging.error(f"Failed to write char to {self.port_name}: {e}")
            return False
    
    def write_string(self, data: str) -> bool:
        """Write string to serial port"""
        if not self.is_connected or not self.port:
            return False
        
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            bytes_written = self.port.write(data)
            self.port.flush()
            return bytes_written > 0
            
        except Exception as e:
            logging.error(f"Failed to write string to {self.port_name}: {e}")
            return False
    
    def write_bytes(self, data: bytes) -> bool:
        """Write bytes to serial port"""
        if not self.is_connected or not self.port:
            return False
        
        try:
            bytes_written = self.port.write(data)
            self.port.flush()
            return bytes_written > 0
            
        except Exception as e:
            logging.error(f"Failed to write bytes to {self.port_name}: {e}")
            return False
    
    def read_char(self) -> Optional[str]:
        """Read single character from serial port"""
        if not self.is_connected or not self.port:
            return None
        
        try:
            data = self.port.read(1)
            if data:
                return data.decode('utf-8')
            return None
            
        except Exception as e:
            logging.error(f"Failed to read char from {self.port_name}: {e}")
            return None
    
    def read_line(self) -> Optional[str]:
        """Read line from serial port"""
        if not self.is_connected or not self.port:
            return None
        
        try:
            data = self.port.readline()
            if data:
                return data.decode('utf-8').strip()
            return None
            
        except Exception as e:
            logging.error(f"Failed to read line from {self.port_name}: {e}")
            return None
    
    def read_bytes(self, size: int) -> Optional[bytes]:
        """Read specified number of bytes from serial port"""
        if not self.is_connected or not self.port:
            return None
        
        try:
            data = self.port.read(size)
            return data if data else None
            
        except Exception as e:
            logging.error(f"Failed to read bytes from {self.port_name}: {e}")
            return None
    
    def clear_buffers(self):
        """Clear input and output buffers"""
        if self.is_connected and self.port:
            try:
                self.port.reset_input_buffer()
                self.port.reset_output_buffer()
            except Exception as e:
                logging.error(f"Failed to clear buffers: {e}")
    
    def start_reading_thread(self, callback=None):
        """Start background thread for reading data"""
        if self.read_thread and self.read_thread.is_alive():
            return
        
        self.read_callback = callback
        self.stop_reading = False
        self.read_thread = threading.Thread(target=self._read_thread_worker)
        self.read_thread.daemon = True
        self.read_thread.start()
    
    def stop_reading_thread(self):
        """Stop background reading thread"""
        if self.read_thread and self.read_thread.is_alive():
            self.stop_reading = True
            self.read_thread.join(timeout=2.0)
    
    def _read_thread_worker(self):
        """Background thread worker for reading data"""
        while not self.stop_reading and self.is_connected:
            try:
                if self.port and self.port.in_waiting > 0:
                    data = self.read_line()
                    if data and self.read_callback:
                        self.read_callback(data)
                time.sleep(0.01)  # Small delay to prevent high CPU usage
            except Exception as e:
                logging.error(f"Error in read thread: {e}")
                break
    
    def send_flat_panel_command(self, command: str) -> bool:
        """Send command to flat panel (F=on, N=off)"""
        if command.upper() in ['F', 'N']:
            return self.write_char(command.upper())
        return False
    
    def get_port_info(self) -> dict:
        """Get current port information"""
        return {
            'port_name': self.port_name,
            'baud_rate': self.baud_rate,
            'data_bits': self.data_bits,
            'stop_bits': self.stop_bits,
            'parity': self.parity,
            'timeout': self.timeout,
            'is_connected': self.is_connected
        }
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        self.disconnect()
