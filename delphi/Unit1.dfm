object Form1: TForm1
  Left = 0
  Top = 0
  Caption = 'QHYCCD Sensor Performance Test Tools'
  ClientHeight = 845
  ClientWidth = 1453
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  OnClose = FormClose
  OnCreate = FormCreate
  PixelsPerInch = 96
  TextHeight = 13
  object Label1: TLabel
    Left = 176
    Top = 630
    Width = 7
    Height = 13
    Caption = 'R'
  end
  object Label2: TLabel
    Left = 175
    Top = 649
    Width = 7
    Height = 13
    Caption = 'G'
  end
  object Label3: TLabel
    Left = 175
    Top = 673
    Width = 6
    Height = 13
    Caption = 'B'
  end
  object Label4: TLabel
    Left = 175
    Top = 479
    Width = 60
    Height = 13
    Caption = #34892#26333#20809#25511#21046
  end
  object Label5: TLabel
    Left = 175
    Top = 503
    Width = 79
    Height = 13
    Caption = 'SVR'#38271#26333#20809#25511#21046
  end
  object Label6: TLabel
    Left = 175
    Top = 527
    Width = 34
    Height = 13
    Caption = 'V blank'
  end
  object Label7: TLabel
    Left = 175
    Top = 551
    Width = 69
    Height = 13
    Caption = 'Crop Start line'
  end
  object Label8: TLabel
    Left = 175
    Top = 575
    Width = 54
    Height = 13
    Caption = 'Crop Width'
  end
  object LabelOffset: TLabel
    Left = 175
    Top = 607
    Width = 62
    Height = 13
    Caption = #36755#20986'OFFSET'
  end
  object Label10: TLabel
    Left = 163
    Top = 723
    Width = 136
    Height = 13
    Caption = 'black dummy clamp start line'
  end
  object Label11: TLabel
    Left = 161
    Top = 754
    Width = 130
    Height = 13
    Caption = 'black dummy clamp number'
  end
  object Label12: TLabel
    Left = 174
    Top = 453
    Width = 48
    Height = 13
    Caption = #25968#23383#22686#30410
  end
  object Label15: TLabel
    Left = 164
    Top = 422
    Width = 24
    Height = 13
    Caption = #22686#30410
  end
  object Label17: TLabel
    Left = 757
    Top = 232
    Width = 48
    Height = 13
    Caption = #20998#39057#31995#25968
  end
  object Label20: TLabel
    Left = 736
    Top = 255
    Width = 69
    Height = 13
    Caption = 'DDR'#32531#20914#38408#20540
  end
  object Label22: TLabel
    Left = 166
    Top = 780
    Width = 62
    Height = 13
    Caption = 'FPGA'#30333#24179#34913
  end
  object Label31: TLabel
    Left = 1192
    Top = 232
    Width = 20
    Height = 13
    Caption = 'gain'
  end
  object Label32: TLabel
    Left = 1192
    Top = 268
    Width = 27
    Height = 13
    Caption = 'gainR'
  end
  object Label33: TLabel
    Left = 1192
    Top = 287
    Width = 27
    Height = 13
    Caption = 'gainG'
  end
  object Label34: TLabel
    Left = 1192
    Top = 308
    Width = 26
    Height = 13
    Caption = 'gainB'
  end
  object ImageFlat1: TImage
    Left = 824
    Top = 8
    Width = 352
    Height = 201
  end
  object ImageFlat2: TImage
    Left = 824
    Top = 215
    Width = 352
    Height = 201
  end
  object ImageBias1: TImage
    Left = 824
    Top = 422
    Width = 352
    Height = 201
  end
  object ImageBias2: TImage
    Left = 824
    Top = 629
    Width = 352
    Height = 201
  end
  object ImagePreview: TImage
    Left = 466
    Top = 8
    Width = 352
    Height = 201
  end
  object LabelTotalReadMode: TLabel
    Left = 78
    Top = 35
    Width = 80
    Height = 13
    Caption = 'LabelTotalModes'
  end
  object Label9: TLabel
    Left = 1311
    Top = 57
    Width = 48
    Height = 13
    Caption = #24320#22987#22686#30410
  end
  object Label13: TLabel
    Left = 1380
    Top = 57
    Width = 48
    Height = 13
    Caption = #22686#30410#38388#38548
  end
  object Button1: TButton
    Left = 342
    Top = 9
    Width = 118
    Height = 49
    Caption = #36830#25509#30456#26426
    TabOrder = 0
    OnClick = Button1Click
  end
  object Button2: TButton
    Left = 8
    Top = 333
    Width = 113
    Height = 36
    Caption = 'Button2'
    TabOrder = 1
    OnClick = Button2Click
  end
  object Edit1: TEdit
    Left = 8
    Top = 72
    Width = 113
    Height = 66
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -48
    Font.Name = 'Tahoma'
    Font.Style = []
    MaxLength = 4
    ParentFont = False
    TabOrder = 2
    Text = '0000'
  end
  object Edit2: TEdit
    Left = 127
    Top = 72
    Width = 114
    Height = 66
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -48
    Font.Name = 'Tahoma'
    Font.Style = []
    MaxLength = 4
    ParentFont = False
    TabOrder = 3
    Text = '0012'
  end
  object Button3: TButton
    Left = 247
    Top = 72
    Width = 58
    Height = 57
    Caption = 'WriteREG'
    TabOrder = 4
    OnClick = Button3Click
  end
  object Edit3: TEdit
    Left = 8
    Top = 143
    Width = 113
    Height = 66
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -48
    Font.Name = 'Tahoma'
    Font.Style = []
    MaxLength = 4
    ParentFont = False
    TabOrder = 5
    Text = '0000'
  end
  object Button4: TButton
    Left = 127
    Top = 143
    Width = 25
    Height = 26
    Caption = '0'
    TabOrder = 6
    OnClick = Button4Click
  end
  object Button5: TButton
    Left = 152
    Top = 143
    Width = 25
    Height = 25
    Caption = '0'
    TabOrder = 7
    OnClick = Button5Click
  end
  object Button6: TButton
    Left = 177
    Top = 143
    Width = 25
    Height = 25
    Caption = '0'
    TabOrder = 8
    OnClick = Button6Click
  end
  object Button7: TButton
    Left = 200
    Top = 143
    Width = 25
    Height = 25
    Caption = '0'
    TabOrder = 9
    OnClick = Button7Click
  end
  object Button8: TButton
    Left = 231
    Top = 143
    Width = 25
    Height = 25
    Caption = '0'
    TabOrder = 10
    OnClick = Button8Click
  end
  object Button9: TButton
    Left = 256
    Top = 143
    Width = 25
    Height = 25
    Caption = '0'
    TabOrder = 11
    OnClick = Button9Click
  end
  object Button10: TButton
    Left = 280
    Top = 143
    Width = 25
    Height = 25
    Caption = '0'
    TabOrder = 12
    OnClick = Button10Click
  end
  object Button11: TButton
    Left = 303
    Top = 143
    Width = 25
    Height = 25
    Caption = '0'
    TabOrder = 13
    OnClick = Button11Click
  end
  object Button12: TButton
    Left = 127
    Top = 174
    Width = 25
    Height = 25
    Caption = '0'
    TabOrder = 14
    OnClick = Button12Click
  end
  object Button13: TButton
    Left = 150
    Top = 174
    Width = 25
    Height = 25
    Caption = '0'
    TabOrder = 15
    OnClick = Button13Click
  end
  object Button14: TButton
    Left = 174
    Top = 174
    Width = 25
    Height = 25
    Caption = '0'
    TabOrder = 16
    OnClick = Button14Click
  end
  object Button15: TButton
    Left = 198
    Top = 174
    Width = 25
    Height = 25
    Caption = '0'
    TabOrder = 17
    OnClick = Button15Click
  end
  object Button16: TButton
    Left = 231
    Top = 174
    Width = 25
    Height = 25
    Caption = '0'
    TabOrder = 18
    OnClick = Button16Click
  end
  object Button17: TButton
    Left = 256
    Top = 174
    Width = 25
    Height = 25
    Caption = '0'
    TabOrder = 19
    OnClick = Button17Click
  end
  object Button18: TButton
    Left = 280
    Top = 174
    Width = 25
    Height = 25
    Caption = '0'
    TabOrder = 20
    OnClick = Button18Click
  end
  object Button19: TButton
    Left = 304
    Top = 174
    Width = 25
    Height = 25
    Caption = '0'
    TabOrder = 21
    OnClick = Button19Click
  end
  object Button20: TButton
    Left = 343
    Top = 143
    Width = 56
    Height = 25
    Caption = 'WriteREG'
    TabOrder = 22
    OnClick = Button20Click
  end
  object Edit4: TEdit
    Left = 8
    Top = 215
    Width = 113
    Height = 66
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -48
    Font.Name = 'Tahoma'
    Font.Style = []
    MaxLength = 4
    ParentFont = False
    TabOrder = 23
    Text = '0000'
  end
  object ScrollBar1: TScrollBar
    Left = 128
    Top = 216
    Width = 271
    Height = 25
    Max = 255
    PageSize = 0
    TabOrder = 24
    OnChange = ScrollBar1Change
  end
  object ScrollBar2: TScrollBar
    Left = 127
    Top = 247
    Width = 272
    Height = 25
    Max = 255
    PageSize = 0
    TabOrder = 25
    Visible = False
    OnChange = ScrollBar2Change
  end
  object Button21: TButton
    Left = 405
    Top = 215
    Width = 128
    Height = 58
    Caption = #20889#23492#23384#22120
    TabOrder = 26
    OnClick = Button21Click
  end
  object Button22: TButton
    Left = 127
    Top = 334
    Width = 115
    Height = 35
    Caption = #21333#24103#25293#25668
    TabOrder = 27
    OnClick = Button22Click
  end
  object ButtonConnectAndMode: TButton
    Left = 10
    Top = 8
    Width = 64
    Height = 49
    Caption = 'ReadMode'
    TabOrder = 28
    OnClick = ButtonConnectAndModeClick
  end
  object Button24: TButton
    Left = 597
    Top = 334
    Width = 172
    Height = 35
    Caption = '16bit_h_1/3sample_7400*1652'
    TabOrder = 29
    OnClick = Button24Click
  end
  object Button25: TButton
    Left = 367
    Top = 333
    Width = 105
    Height = 36
    Caption = 'RESET DDR'
    TabOrder = 30
    OnClick = Button25Click
  end
  object Edit5: TEdit
    Left = 8
    Top = 287
    Width = 113
    Height = 43
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -29
    Font.Name = 'Tahoma'
    Font.Style = []
    MaxLength = 4
    ParentFont = False
    TabOrder = 31
    Text = '0000'
  end
  object ScrollBar3: TScrollBar
    Left = 127
    Top = 287
    Width = 272
    Height = 34
    Max = 65535
    PageSize = 0
    TabOrder = 32
    OnChange = ScrollBar3Change
  end
  object Button26: TButton
    Left = 405
    Top = 285
    Width = 128
    Height = 42
    Caption = #20889#23492#23384#22120
    TabOrder = 33
    OnClick = Button26Click
  end
  object Button27: TButton
    Left = 306
    Top = 72
    Width = 63
    Height = 57
    Caption = 'ReadREG'
    TabOrder = 34
    OnClick = Button27Click
  end
  object SHOWIMG: TButton
    Left = 127
    Top = 375
    Width = 115
    Height = 33
    Caption = 'SHOWIMG'
    TabOrder = 35
  end
  object STOPSHOW: TButton
    Left = 248
    Top = 375
    Width = 113
    Height = 33
    Caption = 'STOPSHOW'
    TabOrder = 36
    OnClick = STOPSHOWClick
  end
  object Button28: TButton
    Left = 342
    Top = 174
    Width = 57
    Height = 25
    Caption = 'ReadREG'
    TabOrder = 37
    OnClick = Button28Click
  end
  object Button29: TButton
    Left = 375
    Top = 72
    Width = 85
    Height = 57
    Caption = #20851#38381#30456#26426
    TabOrder = 38
    OnClick = Button29Click
  end
  object Button30: TButton
    Left = 207
    Top = 32
    Width = 129
    Height = 25
    Caption = #25171#24320#20018#21475#36830#25509
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clBlack
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = []
    ParentFont = False
    TabOrder = 39
    OnClick = Button30Click
  end
  object Button32: TButton
    Left = 367
    Top = 375
    Width = 105
    Height = 33
    Caption = #36864#20986#38271#26333#20809
    TabOrder = 40
    OnClick = Button32Click
  end
  object Button33: TButton
    Left = 8
    Top = 375
    Width = 113
    Height = 33
    Caption = #39640#22686#30410
    TabOrder = 41
    OnClick = Button33Click
  end
  object TrackBar1: TTrackBar
    Left = 8
    Top = 472
    Width = 161
    Height = 25
    Max = 16383
    Position = 1
    TabOrder = 42
    OnChange = TrackBar1Change
  end
  object TrackBar2: TTrackBar
    Left = 6
    Top = 526
    Width = 162
    Height = 25
    Max = 63
    TabOrder = 43
    OnChange = TrackBar2Change
  end
  object TrackBar3: TTrackBar
    Left = 5
    Top = 502
    Width = 163
    Height = 25
    Max = 16383
    Position = 1
    TabOrder = 44
    OnChange = TrackBar3Change
  end
  object TrackBar4: TTrackBar
    Left = 7
    Top = 630
    Width = 153
    Height = 25
    Max = 4095
    Position = 15
    TabOrder = 45
    OnChange = TrackBar4Change
  end
  object TrackBar5: TTrackBar
    Left = 7
    Top = 661
    Width = 153
    Height = 25
    Max = 4095
    Position = 15
    TabOrder = 46
    OnChange = TrackBar5Change
  end
  object TrackBar6: TTrackBar
    Left = 7
    Top = 692
    Width = 153
    Height = 25
    Max = 4095
    Position = 15
    TabOrder = 47
    OnChange = TrackBar6Change
  end
  object trckbr1: TTrackBar
    Left = 7
    Top = 552
    Width = 161
    Height = 16
    Max = 16383
    Position = 1
    TabOrder = 48
    OnChange = trckbr1Change
  end
  object TrackBar7: TTrackBar
    Left = 7
    Top = 575
    Width = 161
    Height = 25
    Max = 16383
    TabOrder = 49
    OnChange = TrackBar7Change
  end
  object TrackBar8: TTrackBar
    Left = 7
    Top = 607
    Width = 161
    Height = 17
    Max = 2047
    Position = 1
    TabOrder = 50
    OnChange = TrackBar8Change
  end
  object TrackBar9: TTrackBar
    Left = 7
    Top = 719
    Width = 150
    Height = 17
    Max = 1023
    TabOrder = 51
    OnChange = TrackBar9Change
  end
  object TrackBar10: TTrackBar
    Left = 5
    Top = 742
    Width = 150
    Height = 25
    Max = 2047
    TabOrder = 52
    OnChange = TrackBar10Change
  end
  object TrackBar11: TTrackBar
    Left = 8
    Top = 441
    Width = 150
    Height = 25
    Max = 1023
    TabOrder = 53
    OnChange = TrackBar11Change
  end
  object CheckBox1: TCheckBox
    Left = 552
    Top = 254
    Width = 97
    Height = 17
    Caption = 'DDR'#27169#24335#20351#33021
    TabOrder = 54
    OnClick = CheckBox1Click
  end
  object TrackBar12: TTrackBar
    Left = 655
    Top = 232
    Width = 65
    Height = 25
    Position = 2
    TabOrder = 55
    OnChange = TrackBar12Change
  end
  object CheckBox2: TCheckBox
    Left = 552
    Top = 231
    Width = 105
    Height = 17
    Caption = #20572#27490#35835#21462'DDR'
    TabOrder = 56
    OnClick = CheckBox2Click
  end
  object TrackBar13: TTrackBar
    Left = 655
    Top = 254
    Width = 64
    Height = 19
    Max = 65530
    Min = 100
    Position = 100
    TabOrder = 57
    OnChange = TrackBar13Change
  end
  object TrackBar14: TTrackBar
    Left = 10
    Top = 773
    Width = 150
    Height = 20
    Max = 255
    Min = 1
    Position = 1
    TabOrder = 58
    OnChange = TrackBar14Change
  end
  object TrackBar15: TTrackBar
    Left = 8
    Top = 799
    Width = 150
    Height = 22
    Max = 255
    Min = 1
    Position = 1
    TabOrder = 59
    OnChange = TrackBar15Change
  end
  object TrackBar16: TTrackBar
    Left = 10
    Top = 827
    Width = 150
    Height = 18
    Max = 255
    Min = 1
    Position = 1
    TabOrder = 60
    OnChange = TrackBar16Change
  end
  object TrackBar17: TTrackBar
    Left = 10
    Top = 856
    Width = 150
    Height = 21
    Max = 255
    Min = 1
    Position = 1
    TabOrder = 61
    OnChange = TrackBar17Change
  end
  object CheckBox3: TCheckBox
    Left = 552
    Top = 300
    Width = 123
    Height = 17
    Caption = 'Reset Slave Control'
    TabOrder = 62
    OnClick = CheckBox3Click
  end
  object TrackBar20: TTrackBar
    Left = 8
    Top = 414
    Width = 150
    Height = 21
    Max = 511
    TabOrder = 63
    OnChange = TrackBar20Change
  end
  object CheckBox6: TCheckBox
    Left = 552
    Top = 277
    Width = 97
    Height = 17
    Caption = 'OB'#33258#21160#26657#27491
    TabOrder = 64
    OnClick = CheckBox6Click
  end
  object Button35: TButton
    Left = 597
    Top = 375
    Width = 172
    Height = 33
    Caption = '8bit_3*3bin_2472*1652'
    TabOrder = 65
    OnClick = Button35Click
  end
  object Button36: TButton
    Left = 478
    Top = 375
    Width = 113
    Height = 31
    Caption = '16bit_roi_7400*1652'
    TabOrder = 66
  end
  object Button45: TButton
    Left = 1192
    Top = 8
    Width = 113
    Height = 48
    Caption = 'LiveFrame'
    TabOrder = 67
    OnClick = Button45Click
  end
  object Memo1: TMemo
    Left = 1200
    Top = 530
    Width = 233
    Height = 287
    Lines.Strings = (
      'Memo1')
    ScrollBars = ssVertical
    TabOrder = 68
    OnChange = Memo1Change
  end
  object ButtonTest: TButton
    Left = 1192
    Top = 112
    Width = 113
    Height = 50
    Caption = 'Run Test'
    TabOrder = 69
    OnClick = ButtonTestClick
  end
  object ButtonAETA: TButton
    Left = 1192
    Top = 62
    Width = 113
    Height = 44
    Caption = 'ExpTimeAdj'
    TabOrder = 70
    OnClick = ButtonAETAClick
  end
  object Button46: TButton
    Left = 1200
    Top = 464
    Width = 75
    Height = 25
    Caption = 'Comport Open'
    TabOrder = 71
    OnClick = Button46Click
  end
  object Button47: TButton
    Left = 1281
    Top = 464
    Width = 75
    Height = 25
    Caption = 'OFF'
    TabOrder = 72
    OnClick = Button47Click
  end
  object Button48: TButton
    Left = 1362
    Top = 464
    Width = 75
    Height = 25
    Caption = 'ON'
    TabOrder = 73
    OnClick = Button48Click
  end
  object Button49: TButton
    Left = 1200
    Top = 499
    Width = 75
    Height = 25
    Caption = 'Clear Memo'
    TabOrder = 74
    OnClick = Button49Click
  end
  object ScrollBar5: TScrollBar
    Left = 1235
    Top = 232
    Width = 198
    Height = 17
    PageSize = 0
    TabOrder = 75
    OnChange = ScrollBar5Change
  end
  object ScrollBar6: TScrollBar
    Left = 1235
    Top = 264
    Width = 198
    Height = 17
    Max = 255
    PageSize = 0
    Position = 128
    TabOrder = 76
    OnChange = ScrollBar6Change
  end
  object ScrollBar7: TScrollBar
    Left = 1235
    Top = 287
    Width = 198
    Height = 17
    Max = 255
    PageSize = 0
    Position = 128
    TabOrder = 77
    OnChange = ScrollBar7Change
  end
  object ScrollBar8: TScrollBar
    Left = 1235
    Top = 310
    Width = 198
    Height = 17
    Max = 255
    PageSize = 0
    Position = 128
    TabOrder = 78
    OnChange = ScrollBar8Change
  end
  object ButtonGain: TButton
    Left = 1192
    Top = 167
    Width = 113
    Height = 25
    Caption = 'Gain'
    TabOrder = 79
    OnClick = ButtonGainClick
  end
  object Button50: TButton
    Left = 1192
    Top = 201
    Width = 41
    Height = 25
    Caption = '0%'
    TabOrder = 80
    OnClick = Button50Click
  end
  object Button51: TButton
    Left = 1235
    Top = 201
    Width = 41
    Height = 25
    Caption = '25%'
    TabOrder = 81
    OnClick = Button51Click
  end
  object Button52: TButton
    Left = 1279
    Top = 201
    Width = 41
    Height = 25
    Caption = '50%'
    TabOrder = 82
    OnClick = Button52Click
  end
  object Button53: TButton
    Left = 1323
    Top = 201
    Width = 41
    Height = 25
    Caption = '75%'
    TabOrder = 83
    OnClick = Button53Click
  end
  object Button54: TButton
    Left = 1370
    Top = 201
    Width = 41
    Height = 25
    Caption = '100%'
    TabOrder = 84
    OnClick = Button54Click
  end
  object Button55: TButton
    Left = 1192
    Top = 354
    Width = 41
    Height = 25
    Caption = '1'
    TabOrder = 85
    OnClick = Button55Click
  end
  object Button56: TButton
    Left = 1239
    Top = 354
    Width = 41
    Height = 25
    Caption = '2'
    TabOrder = 86
    OnClick = Button56Click
  end
  object Button57: TButton
    Left = 1286
    Top = 354
    Width = 41
    Height = 25
    Caption = '5'
    TabOrder = 87
    OnClick = Button57Click
  end
  object Button58: TButton
    Left = 1333
    Top = 354
    Width = 41
    Height = 25
    Caption = '7'
    TabOrder = 88
    OnClick = Button58Click
  end
  object Button59: TButton
    Left = 1380
    Top = 354
    Width = 41
    Height = 25
    Caption = '10'
    TabOrder = 89
    OnClick = Button59Click
  end
  object Button60: TButton
    Left = 1192
    Top = 385
    Width = 41
    Height = 25
    Caption = '15'
    TabOrder = 90
    OnClick = Button60Click
  end
  object Button61: TButton
    Left = 1239
    Top = 385
    Width = 41
    Height = 25
    Caption = '20'
    TabOrder = 91
    OnClick = Button61Click
  end
  object Button62: TButton
    Left = 1286
    Top = 385
    Width = 41
    Height = 25
    Caption = '25'
    TabOrder = 92
    OnClick = Button62Click
  end
  object Button63: TButton
    Left = 1333
    Top = 385
    Width = 41
    Height = 25
    Caption = '30'
    TabOrder = 93
    OnClick = Button63Click
  end
  object Button64: TButton
    Left = 1380
    Top = 385
    Width = 41
    Height = 25
    Caption = '40'
    TabOrder = 94
    OnClick = Button64Click
  end
  object Button65: TButton
    Left = 1192
    Top = 416
    Width = 41
    Height = 25
    Caption = '50'
    TabOrder = 95
    OnClick = Button65Click
  end
  object Button66: TButton
    Left = 1239
    Top = 416
    Width = 41
    Height = 25
    Caption = '100'
    TabOrder = 96
    OnClick = Button66Click
  end
  object Button67: TButton
    Left = 1286
    Top = 416
    Width = 41
    Height = 25
    Caption = '200'
    TabOrder = 97
    OnClick = Button67Click
  end
  object Button68: TButton
    Left = 1333
    Top = 416
    Width = 41
    Height = 25
    Caption = '500'
    TabOrder = 98
    OnClick = Button68Click
  end
  object Button69: TButton
    Left = 1380
    Top = 416
    Width = 41
    Height = 25
    Caption = 'ms'
    TabOrder = 99
  end
  object Button70: TButton
    Left = 1311
    Top = 112
    Width = 110
    Height = 49
    Caption = 'Stop Test'
    TabOrder = 100
    OnClick = Button70Click
  end
  object ComboBoxComPort: TComboBox
    Left = 207
    Top = 8
    Width = 129
    Height = 21
    ItemIndex = 2
    TabOrder = 101
    Text = 'COM3'
    OnChange = ComboBoxComPortChange
    Items.Strings = (
      'COM1'
      'COM2'
      'COM3'
      'COM4'
      'COM5'
      'COM6'
      'COM7'
      'COM8'
      'COM9')
  end
  object MemoGain: TMemo
    Left = 194
    Top = 414
    Width = 120
    Height = 404
    Lines.Strings = (
      'MemoGain')
    TabOrder = 102
  end
  object MemoSystemGain: TMemo
    Left = 194
    Top = 414
    Width = 120
    Height = 404
    Lines.Strings = (
      'MemoSystemGain')
    TabOrder = 103
  end
  object MemoFullWell: TMemo
    Left = 446
    Top = 414
    Width = 120
    Height = 404
    Lines.Strings = (
      'MemoFullWell')
    TabOrder = 104
  end
  object MemoReadoutNoise: TMemo
    Left = 570
    Top = 414
    Width = 120
    Height = 404
    Lines.Strings = (
      'MemoReadoutNoise')
    TabOrder = 105
  end
  object ComboBoxReadMode: TComboBox
    Left = 80
    Top = 8
    Width = 121
    Height = 21
    TabOrder = 106
    Text = 'ComboBoxReadMode'
  end
  object Edit6: TEdit
    Left = 1382
    Top = 8
    Width = 63
    Height = 43
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -29
    Font.Name = 'Tahoma'
    Font.Style = []
    ParentFont = False
    TabOrder = 107
    Text = '5'
    OnChange = Edit6Change
  end
  object Edit7: TEdit
    Left = 1311
    Top = 8
    Width = 65
    Height = 43
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -29
    Font.Name = 'Tahoma'
    Font.Style = []
    ParentFont = False
    TabOrder = 108
    Text = '0'
    OnChange = Edit7Change
  end
  object MemoReadoutNoise2: TMemo
    Left = 696
    Top = 414
    Width = 122
    Height = 404
    Lines.Strings = (
      'MemoReadoutNoise2')
    TabOrder = 109
  end
  object Timer1: TTimer
    Enabled = False
    Interval = 50
    OnTimer = Timer1Timer
    Left = 696
    Top = 288
  end
  object FComPort1: TComPort
    Active = False
    BaudRate = br9600
    DataBits = db8
    DeviceName = 'COM3'
    Options = []
    Left = 760
    Top = 280
  end
end
