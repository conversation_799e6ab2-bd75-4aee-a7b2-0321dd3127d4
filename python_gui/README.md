# QHYCCD Sensor Performance Test Tools - Python GUI

This is a Python implementation of the QHYCCD camera control and testing application, replicating all functionality from the original Delphi application.

## Features

### Camera Control
- **Connection Management**: Connect/disconnect QHYCCD cameras
- **Read Mode Selection**: Support for multiple camera read modes
- **Live Preview**: Real-time camera preview with adjustable frame rate
- **Parameter Control**: Full control over camera parameters including:
  - Gain (analog and digital)
  - Exposure time
  - Offset
  - RGB white balance gains
  - USB traffic control
  - DDR mode
  - Transfer bit depth

### Register Operations
- **Direct Register Access**: Read/write camera registers using hex addresses
- **Bit Manipulation**: Interactive bit-level register editing with visual bit buttons
- **Parameter Adjustment**: Real-time parameter adjustment with sliders and scrollbars

### Image Processing & Analysis
- **Multi-Image Display**: Preview, flat frames, and bias frames display
- **Statistical Analysis**: Calculate mean, standard deviation, RMS values
- **Histogram Generation**: Image histogram analysis
- **Noise Analysis**: Readout noise and total noise measurements
- **Gain Measurement**: Photon transfer curve analysis for gain calculation

### Test Automation
- **Flat/Bias Frame Testing**: Automated capture and analysis of calibration frames
- **Exposure Time Adjustment**: Automatic exposure time optimization for target signal levels
- **Gain Testing**: Systematic gain measurement across different settings
- **Readout Noise Testing**: Comprehensive noise characterization
- **Full Well Testing**: Saturation point determination

### Serial Communication
- **COM Port Control**: Connect to external devices (flat panels, etc.)
- **Flat Panel Control**: Automated flat panel on/off commands
- **Multi-Port Support**: Support for multiple COM ports with auto-detection

### User Interface
- **Intuitive Layout**: Organized control panels matching original Delphi interface
- **Real-time Updates**: Live parameter feedback and status updates
- **Progress Monitoring**: Test progress indication with detailed logging
- **Error Handling**: Comprehensive error reporting and recovery

## Installation

### Prerequisites
- Python 3.7 or higher
- QHYCCD SDK and drivers installed
- Required Python packages (see requirements.txt)

### Setup
1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

2. Ensure QHYCCD SDK is installed and `qhyccd.dll` is accessible
3. Connect your QHYCCD camera
4. Run the application:
```bash
python main.py
```

## File Structure

```
python_gui/
├── main.py                 # Main GUI application
├── camera_controller.py    # QHYCCD camera interface
├── serial_controller.py    # Serial port communication
├── image_processor.py      # Image processing and analysis
├── test_automation.py      # Automated testing procedures
├── utils.py               # Utility functions
├── requirements.txt       # Python dependencies
└── README.md             # This file
```

## Usage Guide

### Basic Operation
1. **Connect Camera**: Click "连接相机" to scan and connect to available cameras
2. **Select Read Mode**: Choose appropriate read mode from dropdown
3. **Setup Serial**: Select COM port and click "打开串口连接" for flat panel control
4. **Start Live Preview**: Click "SHOWIMG" to begin real-time preview

### Register Operations
1. **Direct Access**: Enter hex address and value, click WriteREG/ReadREG
2. **Bit Manipulation**: Use bit buttons to toggle individual bits, then write to register
3. **Parameter Adjustment**: Use sliders for real-time parameter changes

### Automated Testing
1. **Exposure Adjustment**: Click "ExpTimeAdj" to automatically optimize exposure time
2. **Run Test**: Click "Run Test" to start comprehensive flat/bias frame analysis
3. **Monitor Progress**: Watch status updates in the log area
4. **Stop Test**: Click "Stop Test" to halt any running test

### Parameter Control
- **Gain Control**: Use gain sliders or percentage buttons for quick settings
- **Exposure Time**: Click time buttons (1-500ms) for common exposure settings
- **RGB Gains**: Adjust individual color channel gains
- **Advanced Parameters**: Use trackbars for fine control of all camera parameters

## Technical Details

### Camera Interface
The application uses the QHYCCD SDK through Python ctypes bindings, providing:
- Direct access to all QHYCCD API functions
- Automatic resource management
- Error handling and recovery
- Multi-threading support for live preview

### Image Processing
Built on OpenCV and NumPy for:
- High-performance image processing
- Statistical analysis
- Format conversion (16-bit to 8-bit for display)
- ROI-based calculations

### Test Automation
Implements sophisticated testing algorithms:
- Photon transfer curve analysis for gain measurement
- Automatic exposure time optimization using RMS feedback
- Multi-frame noise analysis
- Comprehensive performance characterization

### Serial Communication
Robust serial port handling:
- Auto-detection of available ports
- Configurable communication parameters
- Thread-safe operation
- Error recovery and reconnection

## Compatibility

### Supported Cameras
- All QHYCCD cameras supported by the QHYCCD SDK
- Tested with QHY5II, QHY367C, and other models
- Automatic detection of camera capabilities

### Operating Systems
- Windows (primary platform)
- Linux (with appropriate QHYCCD SDK)
- macOS (with appropriate QHYCCD SDK)

### Python Versions
- Python 3.7+
- Tested with Python 3.8, 3.9, 3.10

## Troubleshooting

### Common Issues
1. **Camera Not Detected**: Ensure QHYCCD drivers are installed and camera is connected
2. **DLL Load Error**: Verify qhyccd.dll is in system PATH or application directory
3. **Serial Port Issues**: Check COM port availability and permissions
4. **Image Display Problems**: Verify OpenCV and PIL installation

### Debug Mode
Enable detailed logging by modifying the logging level in main.py:
```python
logging.basicConfig(level=logging.DEBUG)
```

## Development

### Architecture
The application follows a modular design:
- **Separation of Concerns**: Each module handles specific functionality
- **Event-Driven**: GUI events trigger appropriate controller actions
- **Thread-Safe**: Multi-threading for non-blocking operations
- **Error Resilient**: Comprehensive exception handling

### Extending Functionality
To add new features:
1. Add new methods to appropriate controller classes
2. Create GUI elements in main.py
3. Connect GUI events to controller methods
4. Update test automation if needed

### Contributing
1. Follow Python PEP 8 style guidelines
2. Add comprehensive error handling
3. Include logging for debugging
4. Test with multiple camera models
5. Update documentation

## License

This software is provided as-is for educational and research purposes. Please ensure compliance with QHYCCD SDK licensing terms.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review log files for error details
3. Verify hardware connections and drivers
4. Test with original Delphi application for comparison
