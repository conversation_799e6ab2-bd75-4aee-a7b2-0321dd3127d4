//---------------------------------------------------------------------------

#pragma hdrstop

#include "qhyccd.h"

#include <windows.h>
//#include <FMX.Dialogs.hpp>
//---------------------------------------------------------------------------
#pragma package(smart_init)

FuncInitQHYCCDResource              InitQHYCCDResource;
FuncReleaseQHYCCDResource           ReleaseQHYCCDResource;
FuncScanQHYCCD                      ScanQHYCCD;
FuncGetQHYCCDId                     GetQHYCCDId;
FuncOpenQHYCCD                      OpenQHYCCD;
FuncCloseQHYCCD                     CloseQHYCCD;
FuncInitQHYCCD                      InitQHYCCD;
FuncSetQHYCCDParam                  SetQHYCCDParam;
FuncSetQHYCCDBinMode                SetQHYCCDBinMode;
FuncGetQHYCCDMemLength              GetQHYCCDMemLength;
FuncExpQHYCCDSingleFrame            ExpQHYCCDSingleFrame;
FuncGetQHYCCDSingleFrame            GetQHYCCDSingleFrame;
FuncBeginQHYCCDLive                 BeginQHYCCDLive;
FuncGetQHYCCDLiveFrame              GetQHYCCDLiveFrame;
FuncCancelQHYCCDExposingAndReadout  CancelQHYCCDExposingAndReadout;
FuncSetQHYCCDStreamMode             SetQHYCCDStreamMode;
FuncSetQHYCCDResolution             SetQHYCCDResolution;
FuncStopQHYCCDLive                  StopQHYCCDLive;
FuncIsQHYCCDControlAvailable        IsQHYCCDControlAvailable;
FuncGetQHYCCDChipInfo               GetQHYCCDChipInfo;
FuncGetQHYCCDParam                  GetQHYCCDParam;
FuncQHYCCDVendRequestWrite          QHYCCDVendRequestWrite;
FuncQHYCCDVendRequestRead           QHYCCDVendRequestRead;
FuncQHYCCDReadUSB_SYNC              QHYCCDReadUSB_SYNC;

FuncGetQHYCCDNumberOfReadModes      GetQHYCCDNumberOfReadModes ;
FuncGetQHYCCDReadModeResolution     GetQHYCCDReadModeResolution ;
FuncGetQHYCCDReadModeName           GetQHYCCDReadModeName   ;
FuncSetQHYCCDReadMode               SetQHYCCDReadMode     ;
FuncGetQHYCCDReadMode               GetQHYCCDReadMode    ;
//HMODULE hDll;
HINSTANCE hDll;

void QHYCCDImport()
{
	hDll = ::LoadLibrary("qhyccd.dll");
	if(!hDll)
	{
		//ShowMessage("����qhyccd.dllʧ��");
	}
	else
	{

		InitQHYCCDResource = (FuncInitQHYCCDResource)::GetProcAddress(hDll, "_InitQHYCCDResource@0");
		if(!InitQHYCCDResource)
		{
			//ShowMessage("��ȡInitQHYCCDResource������ַʧ��");
			MessageBox(NULL,"InitQHYCCDResource","",0);
		}
		ReleaseQHYCCDResource = (FuncReleaseQHYCCDResource)::GetProcAddress(hDll, "_ReleaseQHYCCDResource@0");
		if(!ReleaseQHYCCDResource)
		{
			MessageBox(NULL,"ReleaseQHYCCDResource","",0);
		}
		ScanQHYCCD = (FuncScanQHYCCD)::GetProcAddress(hDll, "_ScanQHYCCD@0");
		if(!ScanQHYCCD)
		{
			MessageBox(NULL,"ScanQHYCCD","",0);
		}
		GetQHYCCDId = (FuncGetQHYCCDId)::GetProcAddress(hDll, "_GetQHYCCDId@8");
		if(!GetQHYCCDId)
		{
			MessageBox(NULL,"GetQHYCCDId","",0);
		}
		OpenQHYCCD = (FuncOpenQHYCCD)::GetProcAddress(hDll, "_OpenQHYCCD@4");
		if(!OpenQHYCCD)
		{
			MessageBox(NULL,"OpenQHYCCD","",0);
		}
		CloseQHYCCD = (FuncCloseQHYCCD)::GetProcAddress(hDll, "_CloseQHYCCD@4");
		if(!CloseQHYCCD)
		{
			MessageBox(NULL,"CloseQHYCCD","",0);
		}
		InitQHYCCD = (FuncInitQHYCCD)::GetProcAddress(hDll, "_InitQHYCCD@4");
		if(!InitQHYCCD)
		{
			MessageBox(NULL,"InitQHYCCD","",0);
		}
		SetQHYCCDParam = (FuncSetQHYCCDParam)::GetProcAddress(hDll, "_SetQHYCCDParam@16");
		if(!SetQHYCCDParam)
		{
			MessageBox(NULL,"SetQHYCCDParam","",0);
		}
		SetQHYCCDBinMode = (FuncSetQHYCCDBinMode)::GetProcAddress(hDll, "_SetQHYCCDBinMode@12");
		if(!SetQHYCCDBinMode)
		{
			MessageBox(NULL,"SetQHYCCDBinMode","",0);
		}
		GetQHYCCDMemLength = (FuncGetQHYCCDMemLength)::GetProcAddress(hDll, "_GetQHYCCDMemLength@4");
		if(!GetQHYCCDMemLength)
		{
			MessageBox(NULL,"GetQHYCCDMemLength","",0);
		}
		ExpQHYCCDSingleFrame = (FuncExpQHYCCDSingleFrame)::GetProcAddress(hDll, "_ExpQHYCCDSingleFrame@4");
		if(!ExpQHYCCDSingleFrame)
		{
			MessageBox(NULL,"ExpQHYCCDSingleFrame","",0);
		}
		GetQHYCCDSingleFrame = (FuncGetQHYCCDSingleFrame)::GetProcAddress(hDll, "_GetQHYCCDSingleFrame@24");
		if(!GetQHYCCDSingleFrame)
		{
			MessageBox(NULL,"GetQHYCCDSingleFrame","",0);
		}
		BeginQHYCCDLive = (FuncBeginQHYCCDLive)::GetProcAddress(hDll, "_BeginQHYCCDLive@4");
		if(!BeginQHYCCDLive)
		{
			MessageBox(NULL,"BeginQHYCCDLive","",0);
		}
		GetQHYCCDLiveFrame = (FuncGetQHYCCDLiveFrame)::GetProcAddress(hDll, "_GetQHYCCDLiveFrame@24");
		if(!GetQHYCCDLiveFrame)
		{
			MessageBox(NULL,"GetQHYCCDLiveFrame","",0);
		}
		CancelQHYCCDExposingAndReadout = (FuncCancelQHYCCDExposingAndReadout)::GetProcAddress(hDll, "_CancelQHYCCDExposingAndReadout@4");
		if(!CancelQHYCCDExposingAndReadout)
		{
			MessageBox(NULL,"CancelQHYCCDExposingAndReadout","",0);
		}
		SetQHYCCDStreamMode = (FuncSetQHYCCDStreamMode)::GetProcAddress(hDll, "_SetQHYCCDStreamMode@8");
		if(!SetQHYCCDStreamMode)
		{
			MessageBox(NULL,"SetQHYCCDStreamMode","",0);
		}
		SetQHYCCDResolution = (FuncSetQHYCCDResolution)::GetProcAddress(hDll, "_SetQHYCCDResolution@20");
		if(!SetQHYCCDResolution)
		{
			MessageBox(NULL,"SetQHYCCDResolution","",0);
		}
		StopQHYCCDLive = (FuncStopQHYCCDLive)::GetProcAddress(hDll, "_StopQHYCCDLive@4");
		if(!StopQHYCCDLive)
		{
			MessageBox(NULL,"StopQHYCCDLive","",0);
		}
		IsQHYCCDControlAvailable = (FuncIsQHYCCDControlAvailable)::GetProcAddress(hDll, "_IsQHYCCDControlAvailable@8");
		if(!StopQHYCCDLive)
		{
			MessageBox(NULL,"IsQHYCCDControlAvailable","",0);
		}
		GetQHYCCDChipInfo = (FuncGetQHYCCDChipInfo)::GetProcAddress(hDll, "_GetQHYCCDChipInfo@32");
		if(!GetQHYCCDChipInfo)
		{
			MessageBox(NULL,"GetQHYCCDChipInfo","",0);
		}

		GetQHYCCDParam = (FuncGetQHYCCDParam)::GetProcAddress(hDll, "_GetQHYCCDParam@8");
		if(!GetQHYCCDParam)
		{
			MessageBox(NULL,"GetQHYCCDParam","",0);
		}

		QHYCCDVendRequestWrite = (FuncQHYCCDVendRequestWrite)::GetProcAddress(hDll, "_QHYCCDVendRequestWrite@24");
		if(!QHYCCDVendRequestWrite)
		{
			MessageBox(NULL,"QHYCCDVendRequestWrite","",0);
		}

		QHYCCDVendRequestRead = (FuncQHYCCDVendRequestRead)::GetProcAddress(hDll, "_QHYCCDVendRequestRead@24");
		if(!QHYCCDVendRequestRead)
		{
			MessageBox(NULL,"QHYCCDVendRequestRead","",0);
		}

		QHYCCDReadUSB_SYNC = (FuncQHYCCDReadUSB_SYNC)::GetProcAddress(hDll, "_QHYCCDReadUSB_SYNC@20");
		if(!QHYCCDReadUSB_SYNC)
		{
			MessageBox(NULL,"QHYCCDReadUSB_SYNC","",0);
		}


		GetQHYCCDNumberOfReadModes  = (FuncGetQHYCCDNumberOfReadModes )::GetProcAddress(hDll, "_GetQHYCCDNumberOfReadModes@20");
		if(!GetQHYCCDNumberOfReadModes )
		{
			MessageBox(NULL,"GetQHYCCDNumberOfReadModes ","",0);
		}

		GetQHYCCDReadModeResolution = (FuncGetQHYCCDReadModeResolution)::GetProcAddress(hDll, "_GetQHYCCDReadModeResolution@20");
		if(!GetQHYCCDReadModeResolution)
		{
			MessageBox(NULL,"GetQHYCCDReadModeResolution","",0);
		}

		GetQHYCCDReadModeName = (FuncGetQHYCCDReadModeName)::GetProcAddress(hDll, "_GetQHYCCDReadModeName@20");
		if(!GetQHYCCDReadModeName)
		{
			MessageBox(NULL,"GetQHYCCDReadModeName","",0);
		}

		QHYCCDSetQHYCCDReadMode = (FuncSetQHYCCDReadMode)::GetProcAddress(hDll, "_SetQHYCCDReadMode@20");
		if(!SetQHYCCDReadMode)
		{
			MessageBox(NULL,"SetQHYCCDReadMode","",0);
		}

		GetQHYCCDReadMode = (FuncGetQHYCCDReadMode)::GetProcAddress(hDll, "_GetQHYCCDReadMode@20");
		if(!GetQHYCCDReadMode)
		{
			MessageBox(NULL,"GetQHYCCDReadMode","",0);
		}








	}
}

void QHYCCDExport()
{
	if(hDll)
	{
		::FreeLibrary(hDll);
	}
}
