"""
QHYCCD Camera Controller - Python wrapper for QHYCCD SDK
This module provides a Python interface to control QHYCCD cameras
"""

import ctypes
import numpy as np
from ctypes import c_char_p, c_uint32, c_double, c_ubyte, POINTER, byref
import os
import logging

# QHYCCD Control IDs (from qhyccd.h)
class ControlID:
    CONTROL_BRIGHTNESS = 0
    CONTROL_CONTRAST = 1
    CONTROL_WBR = 2
    CONTROL_WBB = 3
    CONTROL_WBG = 4
    CONTROL_GAMMA = 5
    CONTROL_GAIN = 6
    CONTROL_OFFSET = 7
    CONTROL_EXPOSURE = 8
    CONTROL_SPEED = 9
    CONTROL_TRANSFERBIT = 10
    CONTROL_CHANNELS = 11
    CONTROL_USBTRAFFIC = 12
    CONTROL_ROWNOISERE = 13
    CONTROL_CURTEMP = 14
    CONTROL_CURPWM = 15
    CONTROL_MANULPWM = 16
    CONTROL_CFWPORT = 17
    CONTROL_COOLER = 18
    CONTROL_ST4PORT = 19
    CONTROL_CALCFWHM = 20
    CONTROL_DEWHEATER = 21
    CONTROL_MANULPWM2 = 22
    CONTROL_MANULPWM3 = 23
    CONTROL_MANULPWM4 = 24
    CONTROL_CFWSLOTSNUM = 25
    CONTROL_ANTIDEWHEATER = 26
    CONTROL_HUMIDITY = 27
    CONTROL_PRESSURE = 28
    CONTROL_VACUUM = 29
    CONTROL_CHIPTEMP = 30
    CONTROL_SENSTEMP = 31
    CONTROL_PCBTEMP = 32
    CONTROL_CONTROL_AMPV = 33
    CONTROL_CONTROL_VCAM = 34
    CONTROL_GPS = 35
    CONTROL_DDR = 36

class QHYCCDController:
    """Main controller class for QHYCCD cameras"""
    
    def __init__(self):
        self.dll = None
        self.camera_handle = None
        self.is_connected = False
        self.camera_id = ""
        self.read_modes = []
        self.current_read_mode = 0
        self.image_width = 0
        self.image_height = 0
        self.image_channels = 1
        self.image_bpp = 16
        self.is_live = False
        
        # Load QHYCCD DLL
        self._load_dll()
    
    def _load_dll(self):
        """Load QHYCCD DLL with architecture detection"""
        import platform
        import sys

        try:
            # Detect Python architecture
            python_arch = platform.architecture()[0]
            is_64bit = python_arch == '64bit'

            logging.info(f"Python architecture: {python_arch}")
            logging.info(f"Python executable: {sys.executable}")

            # List of possible DLL paths to try
            dll_candidates = []

            # Current directory
            current_dir = os.path.dirname(__file__)
            dll_candidates.append(os.path.join(current_dir, 'qhyccd.dll'))

            # Parent directory (delphi folder)
            dll_candidates.append(os.path.join(current_dir, '..', 'delphi', 'qhyccd.dll'))

            # Architecture-specific DLLs
            if is_64bit:
                dll_candidates.append(os.path.join(current_dir, 'qhyccd-64.dll'))
                dll_candidates.append(os.path.join(current_dir, '..', 'delphi', 'qhyccd294-64.dll'))
            else:
                dll_candidates.append(os.path.join(current_dir, 'qhyccd-32.dll'))
                dll_candidates.append(os.path.join(current_dir, '..', 'delphi', 'qhyccd294-32.dll'))

            # System paths
            dll_candidates.append('qhyccd.dll')

            # Try each candidate
            dll_loaded = False
            for dll_path in dll_candidates:
                try:
                    if os.path.exists(dll_path):
                        logging.info(f"Trying to load DLL: {dll_path}")
                        self.dll = ctypes.CDLL(dll_path)
                        dll_loaded = True
                        logging.info(f"Successfully loaded DLL: {dll_path}")
                        break
                    else:
                        logging.debug(f"DLL not found: {dll_path}")
                except Exception as e:
                    logging.warning(f"Failed to load {dll_path}: {e}")
                    continue

            if not dll_loaded:
                # Try WinDLL as fallback (for Windows)
                if sys.platform == 'win32':
                    try:
                        self.dll = ctypes.WinDLL('qhyccd.dll')
                        dll_loaded = True
                        logging.info("Loaded DLL using WinDLL")
                    except Exception as e:
                        logging.error(f"WinDLL fallback failed: {e}")

            if dll_loaded:
                self._setup_dll_functions()
                logging.info("QHYCCD DLL loaded and configured successfully")
            else:
                raise Exception("No compatible QHYCCD DLL found")

        except Exception as e:
            logging.error(f"Failed to load QHYCCD DLL: {e}")
            self.dll = None

            # Provide helpful error message
            if "不是有效的 Win32 应用程序" in str(e) or "is not a valid Win32 application" in str(e):
                logging.error("Architecture mismatch detected!")
                logging.error(f"Python is {python_arch}, but DLL might be for different architecture")
                logging.error("Please ensure you have the correct DLL version:")
                logging.error("- For 64-bit Python: use 64-bit qhyccd.dll")
                logging.error("- For 32-bit Python: use 32-bit qhyccd.dll")
    
    def _setup_dll_functions(self):
        """Setup DLL function prototypes"""
        if not self.dll:
            return
        
        # Define function prototypes
        self.dll.InitQHYCCDResource.restype = c_uint32
        self.dll.ScanQHYCCD.restype = c_uint32
        self.dll.GetQHYCCDId.argtypes = [c_uint32, c_char_p]
        self.dll.GetQHYCCDId.restype = c_uint32
        self.dll.OpenQHYCCD.argtypes = [c_char_p]
        self.dll.OpenQHYCCD.restype = ctypes.c_void_p
        self.dll.CloseQHYCCD.argtypes = [ctypes.c_void_p]
        self.dll.CloseQHYCCD.restype = c_uint32
        self.dll.InitQHYCCD.argtypes = [ctypes.c_void_p]
        self.dll.InitQHYCCD.restype = c_uint32
        self.dll.SetQHYCCDParam.argtypes = [ctypes.c_void_p, c_uint32, c_double]
        self.dll.SetQHYCCDParam.restype = c_uint32
        self.dll.GetQHYCCDParam.argtypes = [ctypes.c_void_p, c_uint32]
        self.dll.GetQHYCCDParam.restype = c_double
        self.dll.SetQHYCCDResolution.argtypes = [ctypes.c_void_p, c_uint32, c_uint32, c_uint32, c_uint32]
        self.dll.SetQHYCCDResolution.restype = c_uint32
        self.dll.BeginQHYCCDLive.argtypes = [ctypes.c_void_p]
        self.dll.BeginQHYCCDLive.restype = c_uint32
        self.dll.StopQHYCCDLive.argtypes = [ctypes.c_void_p]
        self.dll.StopQHYCCDLive.restype = c_uint32
        self.dll.GetQHYCCDLiveFrame.argtypes = [ctypes.c_void_p, POINTER(c_uint32), POINTER(c_uint32), 
                                               POINTER(c_uint32), POINTER(c_uint32), ctypes.c_void_p]
        self.dll.GetQHYCCDLiveFrame.restype = c_uint32
        
        # Read mode functions
        self.dll.GetQHYCCDNumberOfReadModes.argtypes = [ctypes.c_void_p, POINTER(c_uint32)]
        self.dll.GetQHYCCDNumberOfReadModes.restype = c_uint32
        self.dll.GetQHYCCDReadModeName.argtypes = [ctypes.c_void_p, c_uint32, c_char_p]
        self.dll.GetQHYCCDReadModeName.restype = c_uint32
        self.dll.SetQHYCCDReadMode.argtypes = [ctypes.c_void_p, c_uint32]
        self.dll.SetQHYCCDReadMode.restype = c_uint32
        
        # Vendor request functions
        self.dll.QHYCCDVendRequestWrite.argtypes = [ctypes.c_void_p, c_ubyte, c_ubyte, c_uint32, c_uint32, ctypes.c_void_p]
        self.dll.QHYCCDVendRequestWrite.restype = c_uint32
        self.dll.QHYCCDVendRequestRead.argtypes = [ctypes.c_void_p, c_ubyte, c_ubyte, c_uint32, c_uint32, ctypes.c_void_p]
        self.dll.QHYCCDVendRequestRead.restype = c_uint32
    
    def initialize_resources(self):
        """Initialize QHYCCD resources"""
        if not self.dll:
            return False
        
        try:
            result = self.dll.InitQHYCCDResource()
            return result == 0  # QHYCCD_SUCCESS
        except Exception as e:
            logging.error(f"Failed to initialize QHYCCD resources: {e}")
            return False
    
    def scan_cameras(self):
        """Scan for available cameras"""
        if not self.dll:
            return 0
        
        try:
            return self.dll.ScanQHYCCD()
        except Exception as e:
            logging.error(f"Failed to scan cameras: {e}")
            return 0
    
    def get_camera_id(self, index):
        """Get camera ID by index"""
        if not self.dll:
            return ""
        
        try:
            camera_id = ctypes.create_string_buffer(64)
            result = self.dll.GetQHYCCDId(index, camera_id)
            if result == 0:
                return camera_id.value.decode('utf-8')
            return ""
        except Exception as e:
            logging.error(f"Failed to get camera ID: {e}")
            return ""
    
    def connect_camera(self, camera_id=""):
        """Connect to camera"""
        if not self.dll:
            return False
        
        try:
            if not camera_id:
                # Auto-detect first camera
                num_cameras = self.scan_cameras()
                if num_cameras > 0:
                    camera_id = self.get_camera_id(0)
                else:
                    return False
            
            self.camera_handle = self.dll.OpenQHYCCD(camera_id.encode('utf-8'))
            if self.camera_handle:
                self.camera_id = camera_id
                self.is_connected = True
                self._get_read_modes()
                return True
            return False
        except Exception as e:
            logging.error(f"Failed to connect camera: {e}")
            return False
    
    def disconnect_camera(self):
        """Disconnect camera"""
        if self.dll and self.camera_handle:
            try:
                if self.is_live:
                    self.stop_live()
                self.dll.CloseQHYCCD(self.camera_handle)
                self.camera_handle = None
                self.is_connected = False
                self.camera_id = ""
                return True
            except Exception as e:
                logging.error(f"Failed to disconnect camera: {e}")
        return False
    
    def _get_read_modes(self):
        """Get available read modes"""
        if not self.dll or not self.camera_handle:
            return
        
        try:
            num_modes = c_uint32()
            result = self.dll.GetQHYCCDNumberOfReadModes(self.camera_handle, byref(num_modes))
            if result == 0:
                self.read_modes = []
                for i in range(num_modes.value):
                    mode_name = ctypes.create_string_buffer(64)
                    result = self.dll.GetQHYCCDReadModeName(self.camera_handle, i, mode_name)
                    if result == 0:
                        self.read_modes.append(mode_name.value.decode('utf-8'))
        except Exception as e:
            logging.error(f"Failed to get read modes: {e}")
    
    def set_read_mode(self, mode_index):
        """Set camera read mode"""
        if not self.dll or not self.camera_handle:
            return False
        
        try:
            result = self.dll.SetQHYCCDReadMode(self.camera_handle, mode_index)
            if result == 0:
                self.current_read_mode = mode_index
                return True
            return False
        except Exception as e:
            logging.error(f"Failed to set read mode: {e}")
            return False
    
    def init_camera(self):
        """Initialize camera"""
        if not self.dll or not self.camera_handle:
            return False
        
        try:
            result = self.dll.InitQHYCCD(self.camera_handle)
            return result == 0
        except Exception as e:
            logging.error(f"Failed to initialize camera: {e}")
            return False
    
    def set_parameter(self, param_id, value):
        """Set camera parameter"""
        if not self.dll or not self.camera_handle:
            return False
        
        try:
            result = self.dll.SetQHYCCDParam(self.camera_handle, param_id, float(value))
            return result == 0
        except Exception as e:
            logging.error(f"Failed to set parameter {param_id}: {e}")
            return False
    
    def get_parameter(self, param_id):
        """Get camera parameter"""
        if not self.dll or not self.camera_handle:
            return 0.0
        
        try:
            return self.dll.GetQHYCCDParam(self.camera_handle, param_id)
        except Exception as e:
            logging.error(f"Failed to get parameter {param_id}: {e}")
            return 0.0
    
    def set_resolution(self, start_x, start_y, width, height):
        """Set camera resolution"""
        if not self.dll or not self.camera_handle:
            return False
        
        try:
            result = self.dll.SetQHYCCDResolution(self.camera_handle, start_x, start_y, width, height)
            if result == 0:
                self.image_width = width
                self.image_height = height
                return True
            return False
        except Exception as e:
            logging.error(f"Failed to set resolution: {e}")
            return False
    
    def start_live(self):
        """Start live preview"""
        if not self.dll or not self.camera_handle:
            return False
        
        try:
            result = self.dll.BeginQHYCCDLive(self.camera_handle)
            if result == 0:
                self.is_live = True
                return True
            return False
        except Exception as e:
            logging.error(f"Failed to start live: {e}")
            return False
    
    def stop_live(self):
        """Stop live preview"""
        if not self.dll or not self.camera_handle:
            return False
        
        try:
            result = self.dll.StopQHYCCDLive(self.camera_handle)
            if result == 0:
                self.is_live = False
                return True
            return False
        except Exception as e:
            logging.error(f"Failed to stop live: {e}")
            return False
    
    def get_live_frame(self):
        """Get live frame data"""
        if not self.dll or not self.camera_handle or not self.is_live:
            return None
        
        try:
            # Allocate buffer for image data
            buffer_size = self.image_width * self.image_height * 2  # 16-bit
            image_buffer = ctypes.create_string_buffer(buffer_size)
            
            w = c_uint32()
            h = c_uint32()
            bpp = c_uint32()
            channels = c_uint32()
            
            result = self.dll.GetQHYCCDLiveFrame(self.camera_handle, byref(w), byref(h), 
                                                byref(bpp), byref(channels), image_buffer)
            
            if result == 0:
                # Convert to numpy array
                if bpp.value == 16:
                    image_data = np.frombuffer(image_buffer.raw, dtype=np.uint16)
                else:
                    image_data = np.frombuffer(image_buffer.raw, dtype=np.uint8)
                
                image_data = image_data.reshape((h.value, w.value))
                return image_data
            return None
        except Exception as e:
            logging.error(f"Failed to get live frame: {e}")
            return None
    
    def write_register(self, reg_addr, value, length=1):
        """Write to camera register"""
        if not self.dll or not self.camera_handle:
            return False
        
        try:
            data = ctypes.create_string_buffer(length)
            if length == 1:
                data[0] = value & 0xFF
            elif length == 2:
                data[0] = value & 0xFF
                data[1] = (value >> 8) & 0xFF
            
            result = self.dll.QHYCCDVendRequestWrite(self.camera_handle, 0xb8, 0x81, 
                                                    reg_addr, length, data)
            return result == 0
        except Exception as e:
            logging.error(f"Failed to write register: {e}")
            return False
    
    def read_register(self, reg_addr, length=1):
        """Read from camera register"""
        if not self.dll or not self.camera_handle:
            return 0
        
        try:
            data = ctypes.create_string_buffer(length)
            result = self.dll.QHYCCDVendRequestRead(self.camera_handle, 0xb8, 0x81, 
                                                   reg_addr, length, data)
            if result == 0:
                if length == 1:
                    return data[0]
                elif length == 2:
                    return data[0] | (data[1] << 8)
            return 0
        except Exception as e:
            logging.error(f"Failed to read register: {e}")
            return 0
