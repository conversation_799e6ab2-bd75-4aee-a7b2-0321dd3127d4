// CodeGear C++Builder
// Copyright (c) 1995, 2014 by Embarcadero Technologies, Inc.
// All rights reserved

// (DO NOT EDIT: machine generated header) 'Winsoft.FireMonkey.ComPortP.dpk' rev: 28.00 (Windows)

#ifndef Winsoft_Firemonkey_ComportpHPP
#define Winsoft_Firemonkey_ComportpHPP

#pragma delphiheader begin
#pragma option push
#pragma option -w-      // All warnings off
#pragma option -Vx      // Zero-length empty class member 
#pragma pack(push,8)
#include <System.hpp>	// Pascal unit
#include <SysInit.hpp>	// Pascal unit
#include <Winsoft.FireMonkey.ComPort.hpp>	// Pascal unit
#include <Winsoft.FireMonkey.ComPortE.hpp>	// Pascal unit
#include <Winsoft.FireMonkey.ComSignal.hpp>	// Pascal unit
#include <Winsoft.FireMonkey.ComSignalE.hpp>	// Pascal unit
#include <Winapi.Windows.hpp>	// Pascal unit
#include <System.Internal.ExcUtils.hpp>	// Pascal unit
#include <System.SysUtils.hpp>	// Pascal unit
#include <System.VarUtils.hpp>	// Pascal unit
#include <System.Variants.hpp>	// Pascal unit
#include <System.AnsiStrings.hpp>	// Pascal unit
#include <System.Math.hpp>	// Pascal unit
#include <System.Generics.Defaults.hpp>	// Pascal unit
#include <System.Rtti.hpp>	// Pascal unit
#include <System.TypInfo.hpp>	// Pascal unit
#include <System.Classes.hpp>	// Pascal unit
#include <System.Messaging.hpp>	// Pascal unit
#include <System.Actions.hpp>	// Pascal unit
#include <System.Devices.hpp>	// Pascal unit
#include <FMX.MultiResBitmap.hpp>	// Pascal unit
#include <FMX.Graphics.hpp>	// Pascal unit
#include <FMX.TextLayout.hpp>	// Pascal unit
#include <FMX.Types3D.hpp>	// Pascal unit
#include <FMX.Filter.hpp>	// Pascal unit
#include <FMX.Filter.Custom.hpp>	// Pascal unit
#include <FMX.Effects.hpp>	// Pascal unit
#include <FMX.Ani.hpp>	// Pascal unit
#include <FMX.Objects.hpp>	// Pascal unit
#include <FMX.BehaviorManager.hpp>	// Pascal unit
#include <FMX.StdCtrls.hpp>	// Pascal unit
#include <FMX.InertialMovement.hpp>	// Pascal unit
#include <FMX.Layouts.hpp>	// Pascal unit
#include <FMX.Presentation.Factory.hpp>	// Pascal unit
#include <FMX.Controls.Presentation.hpp>	// Pascal unit
#include <FMX.MagnifierGlass.hpp>	// Pascal unit
#include <FMX.Menus.hpp>	// Pascal unit
#include <FMX.Edit.Style.hpp>	// Pascal unit
#include <FMX.Edit.hpp>	// Pascal unit
#include <FMX.Header.hpp>	// Pascal unit
#include <FMX.ListBox.hpp>	// Pascal unit
#include <System.TimeSpan.hpp>	// Pascal unit
#include <System.DateUtils.hpp>	// Pascal unit
#include <FMX.DateTimeCtrls.Types.hpp>	// Pascal unit
#include <FMX.DateTimeCtrls.hpp>	// Pascal unit
#include <FMX.Calendar.Style.hpp>	// Pascal unit
#include <FMX.Calendar.hpp>	// Pascal unit
#include <FMX.Pickers.hpp>	// Pascal unit
#include <FMX.ExtCtrls.hpp>	// Pascal unit
#include <System.IOUtils.hpp>	// Pascal unit
#include <Winapi.ShellAPI.hpp>	// Pascal unit
#include <System.SyncObjs.hpp>	// Pascal unit
#include <Winapi.UxTheme.hpp>	// Pascal unit
#include <FMX.Helpers.Win.hpp>	// Pascal unit
#include <Winapi.GDIPOBJ.hpp>	// Pascal unit
#include <FMX.Canvas.GDIP.hpp>	// Pascal unit
#include <FMX.Printer.hpp>	// Pascal unit
#include <FMX.Dialogs.Win.hpp>	// Pascal unit
#include <Winapi.D2D1.hpp>	// Pascal unit
#include <System.Win.ComObj.hpp>	// Pascal unit
#include <FMX.Canvas.D2D.hpp>	// Pascal unit
#include <FMX.Canvas.GPU.Helpers.hpp>	// Pascal unit
#include <FMX.FontGlyphs.hpp>	// Pascal unit
#include <FMX.TextLayout.GPU.hpp>	// Pascal unit
#include <FMX.Canvas.GPU.hpp>	// Pascal unit
#include <FMX.Context.DX9.hpp>	// Pascal unit
#include <FMX.Context.DX11.hpp>	// Pascal unit
#include <FMX.Platform.Win.hpp>	// Pascal unit
#include <FMX.Gestures.Win.hpp>	// Pascal unit
#include <FMX.Gestures.hpp>	// Pascal unit
#include <FMX.Controls.hpp>	// Pascal unit
#include <FMX.Styles.Objects.hpp>	// Pascal unit
#include <FMX.Styles.Switch.hpp>	// Pascal unit
#include <FMX.Styles.hpp>	// Pascal unit
#include <FMX.Dialogs.hpp>	// Pascal unit
#include <FMX.Platform.hpp>	// Pascal unit
#include <FMX.Types.hpp>	// Pascal unit
#include <FMX.Forms.hpp>	// Pascal unit
#include <System.Win.Registry.hpp>	// Pascal unit
#include <Vcl.Graphics.hpp>	// Pascal unit
#include <Vcl.ActnList.hpp>	// Pascal unit
#include <System.HelpIntfs.hpp>	// Pascal unit
#include <Vcl.GraphUtil.hpp>	// Pascal unit
#include <Vcl.StdCtrls.hpp>	// Pascal unit
#include <Vcl.Printers.hpp>	// Pascal unit
#include <Vcl.Clipbrd.hpp>	// Pascal unit
#include <Vcl.ComCtrls.hpp>	// Pascal unit
#include <Vcl.Dialogs.hpp>	// Pascal unit
#include <Vcl.ExtCtrls.hpp>	// Pascal unit
#include <Vcl.Themes.hpp>	// Pascal unit
#include <Winapi.FlatSB.hpp>	// Pascal unit
#include <Vcl.Forms.hpp>	// Pascal unit
#include <Vcl.Menus.hpp>	// Pascal unit
#include <Vcl.Controls.hpp>	// Pascal unit
#include <IDEMessages.hpp>	// Pascal unit
#include <Vcl.CaptionedDockTree.hpp>	// Pascal unit
#include <Vcl.DockTabSet.hpp>	// Pascal unit
#include <PercentageDockTree.hpp>	// Pascal unit
#include <BrandingAPI.hpp>	// Pascal unit
#include <Vcl.Buttons.hpp>	// Pascal unit
#include <Vcl.ExtDlgs.hpp>	// Pascal unit
#include <Winapi.Mapi.hpp>	// Pascal unit
#include <Vcl.ExtActns.hpp>	// Pascal unit
#include <Vcl.ActnMenus.hpp>	// Pascal unit
#include <Vcl.ActnMan.hpp>	// Pascal unit
#include <Vcl.PlatformDefaultStyleActnCtrls.hpp>	// Pascal unit
#include <BaseDock.hpp>	// Pascal unit
#include <DeskUtil.hpp>	// Pascal unit
#include <DeskForm.hpp>	// Pascal unit
#include <DockForm.hpp>	// Pascal unit
#include <Xml.Win.msxmldom.hpp>	// Pascal unit
#include <Xml.xmldom.hpp>	// Pascal unit
#include <ToolsAPI.hpp>	// Pascal unit
#include <Proxies.hpp>	// Pascal unit
#include <DesignEditors.hpp>	// Pascal unit
#include <FMX.Colors.hpp>	// Pascal unit

//-- user supplied -----------------------------------------------------------

namespace Winsoft
{
namespace Firemonkey
{
namespace Comportp
{
//-- type declarations -------------------------------------------------------
//-- var, const, procedure ---------------------------------------------------
}	/* namespace Comportp */
}	/* namespace Firemonkey */
}	/* namespace Winsoft */
#if !defined(DELPHIHEADER_NO_IMPLICIT_NAMESPACE_USE) && !defined(NO_USING_NAMESPACE_WINSOFT_FIREMONKEY_COMPORTP)
using namespace Winsoft::Firemonkey::Comportp;
#endif
#if !defined(DELPHIHEADER_NO_IMPLICIT_NAMESPACE_USE) && !defined(NO_USING_NAMESPACE_WINSOFT_FIREMONKEY)
using namespace Winsoft::Firemonkey;
#endif
#if !defined(DELPHIHEADER_NO_IMPLICIT_NAMESPACE_USE) && !defined(NO_USING_NAMESPACE_WINSOFT)
using namespace Winsoft;
#endif
#pragma pack(pop)
#pragma option pop

#pragma delphiheader end.
//-- end unit ----------------------------------------------------------------
#endif	// Winsoft_Firemonkey_ComportpHPP
