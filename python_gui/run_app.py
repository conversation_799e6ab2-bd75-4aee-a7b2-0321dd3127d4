#!/usr/bin/env python3
"""
QHYCCD Test Application Launcher
Simple launcher script with dependency checking and error handling
"""

import sys
import os
import subprocess
import importlib.util

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        print("Error: Python 3.7 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    # Package name -> import name mapping
    package_mapping = {
        'numpy': 'numpy',
        'opencv-python': 'cv2',
        'Pillow': 'PIL',
        'pyserial': 'serial',
        'matplotlib': 'matplotlib'
    }

    missing_packages = []

    for package, import_name in package_mapping.items():
        try:
            importlib.import_module(import_name)
            print(f"✓ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} is missing")

    return missing_packages

def install_dependencies(packages):
    """Install missing dependencies"""
    if not packages:
        return True
    
    print(f"\nInstalling missing packages: {', '.join(packages)}")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + packages)
        print("✓ All packages installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install packages: {e}")
        return False

def check_qhyccd_dll():
    """Check if QHYCCD DLL is available"""
    dll_paths = [
        os.path.join(os.path.dirname(__file__), '..', 'delphi', 'qhyccd.dll'),
        'qhyccd.dll',
        os.path.join(os.environ.get('SYSTEMROOT', ''), 'System32', 'qhyccd.dll'),
        os.path.join(os.environ.get('SYSTEMROOT', ''), 'SysWOW64', 'qhyccd.dll')
    ]
    
    for dll_path in dll_paths:
        if os.path.exists(dll_path):
            print(f"✓ QHYCCD DLL found at: {dll_path}")
            return True
    
    print("⚠ QHYCCD DLL not found in common locations")
    print("Please ensure qhyccd.dll is installed or in the application directory")
    return False

def main():
    """Main launcher function"""
    print("QHYCCD Sensor Performance Test Tools - Python GUI")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        input("Press Enter to exit...")
        return 1
    
    # Check dependencies
    print("\nChecking dependencies...")
    missing_packages = check_dependencies()
    
    if missing_packages:
        print(f"\nMissing packages detected: {', '.join(missing_packages)}")
        response = input("Would you like to install them automatically? (y/n): ")
        
        if response.lower() in ['y', 'yes']:
            if not install_dependencies(missing_packages):
                input("Press Enter to exit...")
                return 1
        else:
            print("Please install the missing packages manually:")
            print(f"pip install {' '.join(missing_packages)}")
            input("Press Enter to exit...")
            return 1
    
    # Check QHYCCD DLL
    print("\nChecking QHYCCD SDK...")
    check_qhyccd_dll()
    
    # Launch application
    print("\nLaunching application...")
    try:
        # Import and run the main application
        from main import QHYCCDTestApp
        
        print("✓ Application modules loaded successfully")
        print("Starting GUI...")
        
        app = QHYCCDTestApp()
        app.run()
        
    except ImportError as e:
        print(f"✗ Failed to import application modules: {e}")
        print("Please ensure all files are in the correct location")
        input("Press Enter to exit...")
        return 1
    
    except Exception as e:
        print(f"✗ Application error: {e}")
        print("Check the log for more details")
        input("Press Enter to exit...")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
