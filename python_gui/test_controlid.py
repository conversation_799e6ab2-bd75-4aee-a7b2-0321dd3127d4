#!/usr/bin/env python3
"""
ControlID Test Script
Test if ControlID import and usage is working correctly
"""

import sys
import os

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_controlid_import():
    """Test ControlID import"""
    print("Testing ControlID import...")
    
    try:
        from camera_controller import ControlID
        print("✅ ControlID imported successfully")
        
        # Test some common control IDs
        test_controls = [
            'CONTROL_GAIN',
            'CONTROL_EXPOSURE', 
            'CONTROL_OFFSET',
            'CONTROL_WBR',
            'CONTROL_WBG',
            'CONTROL_WBB',
            'CONTROL_DDR',
            'CONTROL_USBTRAFFIC',
            'CONTROL_TRANSFERBIT'
        ]
        
        print("\nTesting ControlID attributes:")
        for control in test_controls:
            if hasattr(ControlID, control):
                value = getattr(ControlID, control)
                print(f"✅ ControlID.{control} = {value}")
            else:
                print(f"❌ ControlID.{control} not found")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import ControlID: {e}")
        return False

def test_camera_controller():
    """Test QHYCCDController import"""
    print("\nTesting QHYCCDController import...")
    
    try:
        from camera_controller import QHYCCDController
        print("✅ QHYCCDController imported successfully")
        
        # Create controller instance (without connecting)
        controller = QHYCCDController()
        print("✅ QHYCCDController instance created")
        
        # Check if it has required methods
        required_methods = [
            'initialize_resources',
            'scan_cameras',
            'connect_camera',
            'set_parameter',
            'get_parameter'
        ]
        
        print("\nTesting QHYCCDController methods:")
        for method in required_methods:
            if hasattr(controller, method):
                print(f"✅ {method}() method exists")
            else:
                print(f"❌ {method}() method missing")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to test QHYCCDController: {e}")
        return False

def test_test_automation():
    """Test TestAutomation import"""
    print("\nTesting TestAutomation import...")
    
    try:
        from test_automation import TestAutomation, TestType
        print("✅ TestAutomation imported successfully")
        
        # Test TestType enum
        test_types = [
            'FLAT_BIAS_TEST',
            'GAIN_TEST',
            'EXPOSURE_ADJUSTMENT',
            'READOUT_NOISE_TEST',
            'FULL_WELL_TEST'
        ]
        
        print("\nTesting TestType enum:")
        for test_type in test_types:
            if hasattr(TestType, test_type):
                value = getattr(TestType, test_type)
                print(f"✅ TestType.{test_type} = {value}")
            else:
                print(f"❌ TestType.{test_type} not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to test TestAutomation: {e}")
        return False

def test_integration():
    """Test integration between modules"""
    print("\nTesting module integration...")
    
    try:
        from camera_controller import QHYCCDController, ControlID
        from serial_controller import SerialController
        from image_processor import ImageProcessor
        from test_automation import TestAutomation, TestType
        
        print("✅ All modules imported successfully")
        
        # Create instances
        camera = QHYCCDController()
        serial = SerialController()
        image_processor = ImageProcessor()
        test_automation = TestAutomation(camera, serial, image_processor)
        
        print("✅ All instances created successfully")
        
        # Test ControlID usage in test_automation
        print("\nTesting ControlID usage in TestAutomation...")
        
        # This should not raise an error now
        test_params = {
            'flat_exposure_time': 45000,
            'bias_exposure_time': 1000,
            'num_flat_frames': 2,
            'num_bias_frames': 2
        }
        
        # Test that we can access ControlID constants
        exposure_control = ControlID.CONTROL_EXPOSURE
        gain_control = ControlID.CONTROL_GAIN
        
        print(f"✅ CONTROL_EXPOSURE = {exposure_control}")
        print(f"✅ CONTROL_GAIN = {gain_control}")
        
        print("✅ Integration test passed")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("ControlID and Module Integration Test")
    print("=" * 45)
    
    all_tests_passed = True
    
    # Run tests
    tests = [
        ("ControlID Import", test_controlid_import),
        ("Camera Controller", test_camera_controller),
        ("Test Automation", test_test_automation),
        ("Integration", test_integration)
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if not test_func():
            all_tests_passed = False
    
    # Summary
    print("\n" + "=" * 45)
    if all_tests_passed:
        print("🎉 ALL TESTS PASSED!")
        print("The ControlID issue has been fixed.")
        print("You can now run the application and use the Run Test button.")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please check the errors above.")
    
    print("\n" + "=" * 45)
    input("Press Enter to exit...")
    return 0 if all_tests_passed else 1

if __name__ == "__main__":
    sys.exit(main())
