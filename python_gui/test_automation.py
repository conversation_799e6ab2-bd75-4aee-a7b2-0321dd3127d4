"""
Test Automation Module
Handles automated testing procedures for camera performance evaluation
"""

import time
import threading
import logging
from typing import List, Dict, Callable, Optional
import numpy as np
from enum import Enum

# Import ControlID from camera_controller
from camera_controller import ControlID

class TestState(Enum):
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    ERROR = "error"

class TestType(Enum):
    FLAT_BIAS_TEST = "flat_bias_test"
    GAIN_TEST = "gain_test"
    EXPOSURE_ADJUSTMENT = "exposure_adjustment"
    READOUT_NOISE_TEST = "readout_noise_test"
    FULL_WELL_TEST = "full_well_test"

class TestAutomation:
    """Automated testing controller"""
    
    def __init__(self, camera_controller, serial_controller, image_processor):
        self.camera = camera_controller
        self.serial = serial_controller
        self.image_processor = image_processor
        
        # Test state
        self.current_test = None
        self.test_state = TestState.IDLE
        self.test_thread = None
        self.stop_test = False
        
        # Test parameters
        self.test_params = {}
        self.test_results = {}
        
        # Callbacks
        self.progress_callback = None
        self.result_callback = None
        self.image_callback = None
        
        # Test data storage
        self.flat_frames = []
        self.bias_frames = []
        self.test_images = []
        
        # Default test parameters
        self.default_params = {
            'flat_exposure_time': 45000,  # microseconds
            'bias_exposure_time': 1000,   # microseconds
            'num_flat_frames': 10,
            'num_bias_frames': 10,
            'gain_start': 0,
            'gain_step': 10,
            'gain_max': 100,
            'target_rms': 30000,
            'max_rms': 60000,
            'min_rms': 25000,
            'roi_x': 400,
            'roi_y': 400,
            'roi_width': 500,
            'roi_height': 500
        }
    
    def set_callbacks(self, progress_callback: Callable = None, 
                     result_callback: Callable = None,
                     image_callback: Callable = None):
        """Set callback functions for test updates"""
        self.progress_callback = progress_callback
        self.result_callback = result_callback
        self.image_callback = image_callback
    
    def start_test(self, test_type: TestType, params: Dict = None) -> bool:
        """Start automated test"""
        if self.test_state == TestState.RUNNING:
            logging.warning("Test already running")
            return False
        
        if not self.camera.is_connected:
            logging.error("Camera not connected")
            return False
        
        # Set test parameters
        self.test_params = self.default_params.copy()
        if params:
            self.test_params.update(params)
        
        # Initialize test
        self.current_test = test_type
        self.test_state = TestState.RUNNING
        self.stop_test = False
        self.test_results = {}
        self.flat_frames = []
        self.bias_frames = []
        self.test_images = []
        
        # Start test thread
        self.test_thread = threading.Thread(target=self._run_test)
        self.test_thread.daemon = True
        self.test_thread.start()
        
        return True
    
    def stop_current_test(self):
        """Stop current test"""
        self.stop_test = True
        if self.test_thread and self.test_thread.is_alive():
            self.test_thread.join(timeout=5.0)
        self.test_state = TestState.IDLE
    
    def pause_test(self):
        """Pause current test"""
        if self.test_state == TestState.RUNNING:
            self.test_state = TestState.PAUSED
    
    def resume_test(self):
        """Resume paused test"""
        if self.test_state == TestState.PAUSED:
            self.test_state = TestState.RUNNING
    
    def _run_test(self):
        """Main test execution thread"""
        try:
            if self.current_test == TestType.FLAT_BIAS_TEST:
                self._run_flat_bias_test()
            elif self.current_test == TestType.GAIN_TEST:
                self._run_gain_test()
            elif self.current_test == TestType.EXPOSURE_ADJUSTMENT:
                self._run_exposure_adjustment()
            elif self.current_test == TestType.READOUT_NOISE_TEST:
                self._run_readout_noise_test()
            elif self.current_test == TestType.FULL_WELL_TEST:
                self._run_full_well_test()
            
            if not self.stop_test:
                self.test_state = TestState.COMPLETED
                self._notify_progress("Test completed successfully", 100)
            
        except Exception as e:
            logging.error(f"Test error: {e}")
            self.test_state = TestState.ERROR
            self._notify_progress(f"Test error: {e}", -1)
    
    def _run_flat_bias_test(self):
        """Run flat and bias frame test"""
        self._notify_progress("Starting flat/bias test", 0)
        
        # Setup camera
        self._setup_camera_for_test()
        
        # Turn on flat panel
        if self.serial.is_connected:
            self.serial.send_flat_panel_command('F')
            time.sleep(1.0)
        
        # Capture flat frames
        self._notify_progress("Capturing flat frames", 10)
        exposure_time = self.test_params['flat_exposure_time']
        self.camera.set_parameter(ControlID.CONTROL_EXPOSURE, exposure_time)
        
        for i in range(self.test_params['num_flat_frames']):
            if self.stop_test:
                return
            
            frame = self._capture_frame()
            if frame is not None:
                self.flat_frames.append(frame)
                if self.image_callback:
                    self.image_callback('flat', frame, i)
            
            progress = 10 + (i + 1) * 40 // self.test_params['num_flat_frames']
            self._notify_progress(f"Captured flat frame {i+1}/{self.test_params['num_flat_frames']}", progress)
            
            time.sleep(0.1)
        
        # Turn off flat panel
        if self.serial.is_connected:
            self.serial.send_flat_panel_command('N')
            time.sleep(1.0)
        
        # Capture bias frames
        self._notify_progress("Capturing bias frames", 50)
        bias_exposure = self.test_params['bias_exposure_time']
        self.camera.set_parameter(ControlID.CONTROL_EXPOSURE, bias_exposure)
        
        for i in range(self.test_params['num_bias_frames']):
            if self.stop_test:
                return
            
            frame = self._capture_frame()
            if frame is not None:
                self.bias_frames.append(frame)
                if self.image_callback:
                    self.image_callback('bias', frame, i)
            
            progress = 50 + (i + 1) * 40 // self.test_params['num_bias_frames']
            self._notify_progress(f"Captured bias frame {i+1}/{self.test_params['num_bias_frames']}", progress)
            
            time.sleep(0.1)
        
        # Analyze results
        self._notify_progress("Analyzing results", 90)
        self._analyze_flat_bias_results()
    
    def _run_gain_test(self):
        """Run gain measurement test"""
        self._notify_progress("Starting gain test", 0)
        
        # Setup camera
        self._setup_camera_for_test()
        
        gain_values = []
        gain_start = self.test_params['gain_start']
        gain_step = self.test_params['gain_step']
        gain_max = self.test_params['gain_max']
        
        current_gain = gain_start
        step_count = 0
        total_steps = (gain_max - gain_start) // gain_step + 1
        
        while current_gain <= gain_max and not self.stop_test:
            self._notify_progress(f"Testing gain {current_gain}", 
                                step_count * 90 // total_steps)
            
            # Set gain
            self.camera.set_parameter(ControlID.CONTROL_GAIN, current_gain)
            time.sleep(0.5)  # Allow gain to settle
            
            # Capture frames for this gain
            flat_frames = []
            bias_frames = []
            
            # Turn on flat panel
            if self.serial.is_connected:
                self.serial.send_flat_panel_command('F')
                time.sleep(1.0)
            
            # Capture flat frames
            for i in range(2):  # Minimum 2 frames for gain calculation
                frame = self._capture_frame()
                if frame is not None:
                    flat_frames.append(frame)
            
            # Turn off flat panel
            if self.serial.is_connected:
                self.serial.send_flat_panel_command('N')
                time.sleep(1.0)
            
            # Capture bias frames
            for i in range(2):
                frame = self._capture_frame()
                if frame is not None:
                    bias_frames.append(frame)
            
            # Calculate gain for this setting
            if len(flat_frames) >= 2 and len(bias_frames) >= 2:
                gain_metrics = self.image_processor.calculate_gain_metrics(flat_frames, bias_frames)
                gain_values.append({
                    'gain_setting': current_gain,
                    'measured_gain': gain_metrics['gain'],
                    'full_well': gain_metrics['full_well']
                })
            
            current_gain += gain_step
            step_count += 1
        
        # Store results
        self.test_results['gain_measurements'] = gain_values
        self._notify_progress("Gain test analysis complete", 100)
    
    def _run_exposure_adjustment(self):
        """Run automatic exposure time adjustment"""
        self._notify_progress("Starting exposure adjustment", 0)
        
        # Setup camera
        self._setup_camera_for_test()
        
        # Turn on flat panel
        if self.serial.is_connected:
            self.serial.send_flat_panel_command('F')
            time.sleep(1.0)
        
        current_exposure = self.test_params['flat_exposure_time']
        target_rms = self.test_params['target_rms']
        max_iterations = 10
        iteration = 0
        
        while iteration < max_iterations and not self.stop_test:
            self._notify_progress(f"Adjusting exposure - iteration {iteration+1}", 
                                iteration * 90 // max_iterations)
            
            # Set exposure time
            self.camera.set_parameter(ControlID.CONTROL_EXPOSURE, current_exposure)
            time.sleep(1.0)  # Allow exposure to settle
            
            # Capture frame
            frame = self._capture_frame()
            if frame is None:
                break
            
            # Calculate RMS in ROI
            roi = (self.test_params['roi_x'], self.test_params['roi_y'],
                   self.test_params['roi_width'], self.test_params['roi_height'])
            stats = self.image_processor.calculate_statistics(frame, roi)
            current_rms = stats['rms']
            
            self._notify_progress(f"Current RMS: {current_rms:.0f}, Target: {target_rms}", 
                                iteration * 90 // max_iterations)
            
            # Check if we're close enough
            if abs(current_rms - target_rms) < target_rms * 0.1:  # Within 10%
                break
            
            # Calculate new exposure time
            if current_rms > self.test_params['max_rms']:
                current_exposure = current_exposure // 2
            elif current_rms > target_rms:
                current_exposure = int(current_exposure * target_rms / current_rms)
            elif current_rms < self.test_params['min_rms'] and current_rms > 0:
                current_exposure = int(current_exposure * target_rms / current_rms)
            
            # Clamp exposure time to reasonable limits
            current_exposure = max(1000, min(current_exposure, 10000000))  # 1ms to 10s
            
            iteration += 1
        
        # Turn off flat panel
        if self.serial.is_connected:
            self.serial.send_flat_panel_command('N')
        
        # Store results
        self.test_results['optimal_exposure'] = current_exposure
        self.test_results['final_rms'] = current_rms
        self._notify_progress("Exposure adjustment complete", 100)
    
    def _run_readout_noise_test(self):
        """Run readout noise measurement test"""
        self._notify_progress("Starting readout noise test", 0)
        
        # Setup camera for bias frames
        self._setup_camera_for_test()
        self.camera.set_parameter(ControlID.CONTROL_EXPOSURE,
                                 self.test_params['bias_exposure_time'])
        
        # Capture multiple bias frames
        bias_frames = []
        num_frames = 20  # More frames for better noise measurement
        
        for i in range(num_frames):
            if self.stop_test:
                return
            
            frame = self._capture_frame()
            if frame is not None:
                bias_frames.append(frame)
            
            progress = (i + 1) * 90 // num_frames
            self._notify_progress(f"Captured bias frame {i+1}/{num_frames}", progress)
            time.sleep(0.1)
        
        # Calculate readout noise
        if len(bias_frames) >= 2:
            noise_metrics = self.image_processor.calculate_noise_metrics(bias_frames[0], bias_frames)
            self.test_results['readout_noise'] = noise_metrics['readout_noise']
            self.test_results['total_noise'] = noise_metrics['total_noise']
        
        self._notify_progress("Readout noise test complete", 100)
    
    def _run_full_well_test(self):
        """Run full well capacity test"""
        self._notify_progress("Starting full well test", 0)
        
        # This test requires capturing frames at different exposure times
        # to find the saturation point
        
        # Setup camera
        self._setup_camera_for_test()
        
        # Turn on flat panel
        if self.serial.is_connected:
            self.serial.send_flat_panel_command('F')
            time.sleep(1.0)
        
        exposure_times = [1000, 5000, 10000, 20000, 50000, 100000, 200000, 500000]
        saturation_data = []
        
        for i, exp_time in enumerate(exposure_times):
            if self.stop_test:
                return
            
            self._notify_progress(f"Testing exposure {exp_time}us", i * 90 // len(exposure_times))
            
            self.camera.set_parameter(ControlID.CONTROL_EXPOSURE, exp_time)
            time.sleep(1.0)
            
            frame = self._capture_frame()
            if frame is not None:
                stats = self.image_processor.calculate_statistics(frame)
                saturation_data.append({
                    'exposure_time': exp_time,
                    'mean_signal': stats['mean'],
                    'max_signal': stats['max']
                })
                
                # Check for saturation
                if stats['max'] > 0.95 * np.iinfo(frame.dtype).max:
                    break
        
        # Turn off flat panel
        if self.serial.is_connected:
            self.serial.send_flat_panel_command('N')
        
        # Store results
        self.test_results['saturation_data'] = saturation_data
        self._notify_progress("Full well test complete", 100)
    
    def _setup_camera_for_test(self):
        """Setup camera with standard test parameters"""
        if not self.camera.is_connected:
            return
        
        # Set standard parameters
        self.camera.set_parameter(ControlID.CONTROL_DDR, 0)
        self.camera.set_parameter(ControlID.CONTROL_OFFSET, 30)
        self.camera.set_parameter(ControlID.CONTROL_USBTRAFFIC, 0)
        self.camera.set_parameter(ControlID.CONTROL_TRANSFERBIT, 16)
        self.camera.set_parameter(ControlID.CONTROL_WBR, 64)
        self.camera.set_parameter(ControlID.CONTROL_WBG, 64)
        self.camera.set_parameter(ControlID.CONTROL_WBB, 64)
    
    def _capture_frame(self) -> Optional[np.ndarray]:
        """Capture single frame from camera"""
        if not self.camera.is_connected:
            return None
        
        # Wait for frame to be ready
        time.sleep(0.1)
        
        # Get frame from camera
        frame = self.camera.get_live_frame()
        return frame
    
    def _analyze_flat_bias_results(self):
        """Analyze flat and bias frame results"""
        if not self.flat_frames or not self.bias_frames:
            return
        
        # Calculate statistics for flat frames
        flat_stats = []
        for frame in self.flat_frames:
            stats = self.image_processor.calculate_statistics(frame)
            flat_stats.append(stats)
        
        # Calculate statistics for bias frames
        bias_stats = []
        for frame in self.bias_frames:
            stats = self.image_processor.calculate_statistics(frame)
            bias_stats.append(stats)
        
        # Calculate noise metrics
        noise_metrics = self.image_processor.calculate_noise_metrics(
            self.bias_frames[0], self.bias_frames)
        
        # Calculate gain metrics
        gain_metrics = self.image_processor.calculate_gain_metrics(
            self.flat_frames, self.bias_frames)
        
        # Store results
        self.test_results.update({
            'flat_stats': flat_stats,
            'bias_stats': bias_stats,
            'noise_metrics': noise_metrics,
            'gain_metrics': gain_metrics
        })
    
    def _notify_progress(self, message: str, progress: int):
        """Notify progress callback"""
        if self.progress_callback:
            self.progress_callback(message, progress)
        logging.info(f"Test progress: {message} ({progress}%)")
    
    def get_test_results(self) -> Dict:
        """Get current test results"""
        return self.test_results.copy()
    
    def is_test_running(self) -> bool:
        """Check if test is currently running"""
        return self.test_state == TestState.RUNNING
