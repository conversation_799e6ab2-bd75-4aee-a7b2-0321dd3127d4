// QCamBc.h

#ifndef _QCAMBC_H_
#define _QCAMBC_H_

#ifdef __cplusplus 
extern "C" {
#endif	

//-----------------------------------------------------------------------------

typedef void (__stdcall *FuncQCamInit)(HANDLE*, int*);
typedef int (__stdcall *FuncQCamGetCamType)(HANDLE);
typedef void (__stdcall *FuncQCamGetFrameSize)(HANDLE, int*, int*);
typedef int (__stdcall *FuncQCamGetMaxFrameLength)(HANDLE);
typedef void (__stdcall *FuncQCamSetBinning)(HANDLE, unsigned char, unsigned char);
typedef void (__stdcall *FuncQCamSetResolution)(HANDLE, int, int);
typedef void (__stdcall *FuncQCamSetDepth)(HANDLE, unsigned char);
typedef void (__stdcall *FuncQCamSetTransferSpeed)(HANDLE, unsigned char);
typedef void (__stdcall *FuncQCamSetUsbTraffic)(HANDLE, unsigned char);
typedef void (__stdcall *FuncQCamSetExposeTime)(HANDLE, double);
typedef void (__stdcall *FuncQCamSetGain)(HANDLE, unsigned short);
typedef void (__stdcall *FuncQCamSetGainRgb)(HANDLE, unsigned short, unsigned short, unsigned short);
typedef BOOL (__stdcall *FuncQCamBeginLive)(HANDLE);
typedef BOOL (__stdcall *FuncQCamBeginSingleExp)(HANDLE);
typedef BOOL (__stdcall *FuncQCamGetLiveFrame)(HANDLE, void*, int*, int*, int*, int*, int*, int*, int*);
typedef BOOL (__stdcall *FuncQCamGetSingleFrame)(HANDLE, long,unsigned long,void*, int*, int*, int*, int*, int*, int*, int*);
typedef BOOL (__stdcall *FuncQCamStopLive)(HANDLE);
typedef BOOL (__stdcall *FuncQCamStopSingleExp)(HANDLE);
typedef void (__stdcall *FuncQCamSetRaw)(HANDLE, BOOL);
typedef void (__stdcall *FuncQCamSetDarkFrameSub)(HANDLE, BOOL);
typedef void (__stdcall *FuncQCamSetDarkFrame)(HANDLE, void* data, int w, int h, int bpp);
typedef void (__stdcall *FuncQCamSetSoftwareBinning)(HANDLE, unsigned char, unsigned char);

typedef void (__stdcall *FuncQHY5IISetLineNoiseRmv)(HANDLE, BOOL);

typedef void (__stdcall *FuncQHY5LIISetHighGainBoost)(HANDLE, BOOL);

typedef BOOL (__stdcall *FuncQDbgVendorReqRead)(HANDLE, unsigned char, unsigned short, unsigned short, unsigned char*, long);
typedef BOOL (__stdcall *FuncQDbgVendorReqWrite)(HANDLE, unsigned char, unsigned short, unsigned short, unsigned char*, long);
typedef BOOL (__stdcall *FuncQDbgInterruptWrite)(HANDLE, unsigned char*, unsigned short);
typedef BOOL (__stdcall *FuncQDbgInterruptWriteEx)(HANDLE, unsigned char*, unsigned short,int);
typedef BOOL (__stdcall *FuncQDbgInterruptRead)(HANDLE, unsigned char*, unsigned short);
typedef BOOL (__stdcall *FuncQDbgInterruptReadEx)(HANDLE, unsigned char*, unsigned short,int);
typedef BOOL (__stdcall *FuncQDbgI2CRead)(HANDLE, unsigned short, unsigned short*);
typedef BOOL(__stdcall *FuncQDbgI2CCacheRead)(HANDLE, unsigned short, unsigned short*);
typedef BOOL (__stdcall *FuncQDbgI2CWrite)(HANDLE, unsigned short, unsigned short);
typedef BOOL (__stdcall *FuncQDbgI2CBatchRead)(HANDLE, unsigned short, unsigned char*, unsigned short);
typedef BOOL (__stdcall *FuncQDbgI2CCacheBatchRead)(HANDLE, unsigned short, unsigned char*, unsigned short);
typedef BOOL (__stdcall *FuncQDbgI2CBatchWrite)(HANDLE, unsigned short, unsigned char*, unsigned short);
typedef unsigned short (__stdcall *FuncQDbgGetFirmwareVersion)(HANDLE);
typedef BOOL (__stdcall *FuncQDbgEepromRead)(HANDLE, unsigned char, unsigned char*, unsigned short);
typedef BOOL (__stdcall *FuncQDbgEepromWrite)(HANDLE, unsigned char, unsigned char*, unsigned short);
typedef void (__stdcall *FuncQDbgSetFinetune)(HANDLE, unsigned char*);
typedef BOOL (__stdcall *FuncQDbgSetUsbTxSize)(HANDLE, int);
typedef BOOL (__stdcall *FuncQDbgBeginLive)(HANDLE);
typedef BOOL (__stdcall *FuncQDbgGetRawData)(HANDLE, void*, int*);
typedef BOOL (__stdcall *FuncQDbgStopLive)(HANDLE);

//-----------------------------------------------------------------------------

extern FuncQCamInit QCamInit;
extern FuncQCamGetCamType QCamGetCamType;
extern FuncQCamGetFrameSize QCamGetFrameSize;
extern FuncQCamGetMaxFrameLength QCamGetMaxFrameLength;
extern FuncQCamSetBinning QCamSetBinning;
extern FuncQCamSetResolution QCamSetResolution;
extern FuncQCamSetDepth QCamSetDepth;
extern FuncQCamSetTransferSpeed QCamSetTransferSpeed;
extern FuncQCamSetUsbTraffic QCamSetUsbTraffic;
extern FuncQCamSetExposeTime QCamSetExposeTime;
extern FuncQCamSetGain QCamSetGain;
extern FuncQCamSetGainRgb QCamSetGainRgb;
extern FuncQCamBeginLive QCamBeginLive;
extern FuncQCamBeginSingleExp QCamBeginSingleExp;
extern FuncQCamGetLiveFrame QCamGetLiveFrame;
extern FuncQCamGetSingleFrame QCamGetSingleFrame;
extern FuncQCamStopLive QCamStopLive;
extern FuncQCamStopSingleExp QCamStopSingleExp;
extern FuncQCamSetRaw QCamSetRaw;
extern FuncQCamSetDarkFrameSub QCamSetDarkFrameSub;
extern FuncQCamSetDarkFrame QCamSetDarkFrame;
extern FuncQCamSetSoftwareBinning QCamSetSoftwareBinning;

extern FuncQHY5IISetLineNoiseRmv QHY5IISetLineNoiseRmv;

extern FuncQHY5LIISetHighGainBoost QHY5LIISetHighGainBoost;

extern FuncQDbgVendorReqRead QDbgVendorReqRead;
extern FuncQDbgVendorReqWrite QDbgVendorReqWrite;
extern FuncQDbgInterruptWrite QDbgInterruptWrite;
extern FuncQDbgInterruptWriteEx QDbgInterruptWriteEx;
extern FuncQDbgInterruptRead QDbgInterruptRead;
extern FuncQDbgInterruptReadEx QDbgInterruptReadEx;
extern FuncQDbgI2CRead QDbgI2CRead;
extern FuncQDbgI2CCacheRead QDbgI2CCacheRead;
extern FuncQDbgI2CWrite QDbgI2CWrite;
extern FuncQDbgI2CBatchRead QDbgI2CBatchRead;
extern FuncQDbgI2CCacheBatchRead QDbgI2CCacheBatchRead;
extern FuncQDbgI2CBatchWrite QDbgI2CBatchWrite;
extern FuncQDbgGetFirmwareVersion QDbgGetFirmwareVersion;
extern FuncQDbgEepromRead QDbgEepromRead;
extern FuncQDbgEepromWrite QDbgEepromWrite;
extern FuncQDbgSetFinetune QDbgSetFinetune;
extern FuncQDbgSetUsbTxSize QDbgSetUsbTxSize;
extern FuncQDbgBeginLive QDbgBeginLive;
extern FuncQDbgGetRawData QDbgGetRawData;
extern FuncQDbgStopLive QDbgStopLive;

//-----------------------------------------------------------------------------

void QCamImport();
void QCamFree();

//-----------------------------------------------------------------------------

#ifdef __cplusplus 
}
#endif

#endif
