#!/usr/bin/env python3
"""
PySerial Diagnostic Script
Check pyserial installation and functionality
"""

import sys
import subprocess

def check_pip_list():
    """Check what packages are installed via pip"""
    print("Checking installed packages...")
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', 'list'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            serial_packages = [line for line in lines if 'serial' in line.lower()]
            if serial_packages:
                print("Serial-related packages found:")
                for pkg in serial_packages:
                    print(f"  {pkg}")
            else:
                print("No serial-related packages found")
            return serial_packages
        else:
            print(f"pip list failed: {result.stderr}")
            return []
    except Exception as e:
        print(f"Error running pip list: {e}")
        return []

def test_serial_imports():
    """Test different ways to import serial"""
    print("\nTesting serial imports...")
    
    # Test 1: Direct import
    try:
        import serial
        print(f"✓ 'import serial' works - version: {serial.__version__}")
    except ImportError as e:
        print(f"✗ 'import serial' failed: {e}")
    except AttributeError:
        print("✓ 'import serial' works but no version info")
    
    # Test 2: Import specific modules
    try:
        import serial.tools.list_ports
        print("✓ 'import serial.tools.list_ports' works")
        
        # Test port listing
        ports = serial.tools.list_ports.comports()
        print(f"  Found {len(ports)} COM ports")
        for port in ports[:3]:
            print(f"    {port.device}: {port.description}")
            
    except ImportError as e:
        print(f"✗ 'import serial.tools.list_ports' failed: {e}")
    except Exception as e:
        print(f"⚠ Serial import works but port listing failed: {e}")
    
    # Test 3: Check installation location
    try:
        import serial
        print(f"✓ Serial module location: {serial.__file__}")
    except:
        pass

def reinstall_pyserial():
    """Try to reinstall pyserial"""
    print("\nAttempting to reinstall pyserial...")
    
    commands = [
        [sys.executable, '-m', 'pip', 'uninstall', 'pyserial', '-y'],
        [sys.executable, '-m', 'pip', 'install', 'pyserial'],
    ]
    
    for cmd in commands:
        try:
            print(f"Running: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print("✓ Command succeeded")
            else:
                print(f"✗ Command failed: {result.stderr}")
        except Exception as e:
            print(f"✗ Command error: {e}")

def main():
    """Main diagnostic function"""
    print("PySerial Diagnostic Tool")
    print("=" * 30)
    print(f"Python version: {sys.version}")
    print(f"Python executable: {sys.executable}")
    print()
    
    # Check installed packages
    packages = check_pip_list()
    
    # Test imports
    test_serial_imports()
    
    # Ask user if they want to reinstall
    print("\n" + "=" * 30)
    if 'pyserial' not in str(packages).lower():
        print("PySerial doesn't appear to be installed.")
        response = input("Would you like to install it? (y/n): ")
        if response.lower() in ['y', 'yes']:
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyserial'])
                print("✓ PySerial installed")
                test_serial_imports()
            except Exception as e:
                print(f"✗ Installation failed: {e}")
    else:
        print("PySerial appears to be installed but import is failing.")
        response = input("Would you like to try reinstalling? (y/n): ")
        if response.lower() in ['y', 'yes']:
            reinstall_pyserial()
            test_serial_imports()
    
    print("\nDiagnostic complete.")
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
