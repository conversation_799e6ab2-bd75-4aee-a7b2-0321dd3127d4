<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <PropertyGroup>
        <ProjectGuid>{CCD021A7-FE34-44AE-99F9-056E892131DC}</ProjectGuid>
        <ProjectVersion>16.1</ProjectVersion>
        <MainSource>QHY5II_I2C_Explore_1.cpp</MainSource>
        <Config Condition="'$(Config)'==''">Debug</Config>
        <FrameworkType>VCL</FrameworkType>
        <Base>True</Base>
        <Platform Condition="'$(Platform)'==''">Win32</Platform>
        <TargetedPlatforms>1</TargetedPlatforms>
        <AppType>Application</AppType>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Base' or '$(Base)'!=''">
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Base)'=='true') or '$(Base_Win32)'!=''">
        <Base_Win32>true</Base_Win32>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Debug' or '$(Cfg_1)'!=''">
        <Cfg_1>true</Cfg_1>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_1)'=='true') or '$(Cfg_1_Win32)'!=''">
        <Cfg_1_Win32>true</Cfg_1_Win32>
        <CfgParent>Cfg_1</CfgParent>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win64' and '$(Cfg_1)'=='true') or '$(Cfg_1_Win64)'!=''">
        <Cfg_1_Win64>true</Cfg_1_Win64>
        <CfgParent>Cfg_1</CfgParent>
        <Cfg_1>true</Cfg_1>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Config)'=='Release' or '$(Cfg_2)'!=''">
        <Cfg_2>true</Cfg_2>
        <CfgParent>Base</CfgParent>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win32' and '$(Cfg_2)'=='true') or '$(Cfg_2_Win32)'!=''">
        <Cfg_2_Win32>true</Cfg_2_Win32>
        <CfgParent>Cfg_2</CfgParent>
        <Cfg_2>true</Cfg_2>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="('$(Platform)'=='Win64' and '$(Cfg_2)'=='true') or '$(Cfg_2_Win64)'!=''">
        <Cfg_2_Win64>true</Cfg_2_Win64>
        <CfgParent>Cfg_2</CfgParent>
        <Cfg_2>true</Cfg_2>
        <Base>true</Base>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base)'!=''">
        <SanitizedProjectName>QHY5II_I2C_Explore_1</SanitizedProjectName>
        <_TCHARMapping>char</_TCHARMapping>
        <VerInfo_Keys>CompanyName=;FileDescription=;FileVersion=*******;InternalName=;LegalCopyright=;LegalTrademarks=;OriginalFilename=;ProductName=;ProductVersion=*******;Comments=</VerInfo_Keys>
        <DCC_Namespace>Vcl;Vcl.Imaging;Vcl.Touch;Vcl.Samples;Vcl.Shell;System;Xml;Data;Datasnap;Web;Soap;$(DCC_Namespace)</DCC_Namespace>
        <Icon_MainIcon>QHY5II_I2C_Explore_1_Icon1.ico</Icon_MainIcon>
        <Manifest_File>None</Manifest_File>
        <VerInfo_Locale>2052</VerInfo_Locale>
        <Multithreaded>true</Multithreaded>
        <LinkPackageImports>rtl.bpi;vcl.bpi</LinkPackageImports>
        <PackageImports>vcl.bpi;rtl.bpi;bcbie.bpi;vclx.bpi;vclactnband.bpi;xmlrtl.bpi;bcbsmp.bpi;dbrtl.bpi;vcldb.bpi;vcldbx.bpi;bdertl.bpi;dsnap.bpi;dsnapcon.bpi;TeeUI.bpi;TeeDB.bpi;Tee.bpi;adortl.bpi;vclib.bpi;ibxpress.bpi;IndyCore.bpi;IndySystem.bpi;IndyProtocols.bpi;inet.bpi;intrawebdb_100_120.bpi;Intraweb_100_120.bpi;vclie.bpi;websnap.bpi;webdsnap.bpi;inetdbbde.bpi;inetdbxpress.bpi;soaprtl.bpi;vclribbon.bpi;dbexpress.bpi;DbxCommonDriver.bpi;DataSnapIndy10ServerTransport.bpi;DataSnapProviderClient.bpi;DataSnapServer.bpi;DbxClientDriver.bpi;DBXInterBaseDriver.bpi;DBXMySQLDriver.bpi;dbxcds.bpi;DBXSybaseASEDriver.bpi;DBXSybaseASADriver.bpi;DBXOracleDriver.bpi;DBXMSSQLDriver.bpi;DBXInformixDriver.bpi;DBXDb2Driver.bpi;RaizeComponentsVcl.bpi;RaizeComponentsVclDb.bpi;$(PackageImports)</PackageImports>
        <AllPackageLibs>rtl.lib;vcl.lib;fmx.lib;vclimg.lib;xmlrtl.lib;vclactnband.lib;vclx.lib</AllPackageLibs>
        <ProjectType>CppVCLApplication</ProjectType>
        <DCC_CBuilderOutput>JPHNE</DCC_CBuilderOutput>
        <DynamicRTL>true</DynamicRTL>
        <UsePackages>true</UsePackages>
        <IncludePath>C:\Program Files (x86)\Winsoft\ComPort\DelphiXE7\;I:\QHY5II_I2C_Explore;$(CG_BOOST_ROOT)\boost\tr1\tr1;$(BDS)\include;$(BDS)\include\vcl;$(CG_BOOST_ROOT);$(IncludePath)</IncludePath>
        <ILINK_LibraryPath>C:\Program Files (x86)\Winsoft\ComPort\DelphiXE7\;I:\QHY5II_I2C_Explore;$(BDS)\lib;$(BDS)\lib\obj;$(BDS)\lib\psdk;$(ILINK_LibraryPath)</ILINK_LibraryPath>
        <BCC_wpar>false</BCC_wpar>
        <BCC_OptimizeForSpeed>true</BCC_OptimizeForSpeed>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Base_Win32)'!=''">
        <ILINK_LibraryPath>D:\Program Files (x86)\Embarcadero\Studio\15.0\lib\win32\release\;D:\WORKSPACE\DOCUMENTS\SensorTestV1\;$(ILINK_LibraryPath)</ILINK_LibraryPath>
        <OutputExt>exe</OutputExt>
        <Icon_MainIcon>QHY5II_I2C_Explore_1_Icon1.ico</Icon_MainIcon>
        <DCC_Namespace>Winapi;System.Win;Data.Win;Datasnap.Win;Web.Win;Soap.Win;Xml.Win;Bde;$(DCC_Namespace)</DCC_Namespace>
        <IncludePath>$(BDSINCLUDE)\windows\vcl;$(IncludePath)</IncludePath>
        <VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
        <VerInfo_Locale>1033</VerInfo_Locale>
        <Manifest_File>$(BDS)\bin\default_app.manifest</Manifest_File>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1)'!=''">
        <BCC_OptimizeForSpeed>false</BCC_OptimizeForSpeed>
        <BCC_DisableOptimizations>true</BCC_DisableOptimizations>
        <DCC_Optimize>false</DCC_Optimize>
        <DCC_DebugInfoInExe>true</DCC_DebugInfoInExe>
        <BCC_InlineFunctionExpansion>false</BCC_InlineFunctionExpansion>
        <IntermediateOutputDir>Debug</IntermediateOutputDir>
        <ILINK_DisableIncrementalLinking>true</ILINK_DisableIncrementalLinking>
        <BCC_UseRegisterVariables>None</BCC_UseRegisterVariables>
        <DCC_Define>DEBUG</DCC_Define>
        <BCC_DebugLineNumbers>true</BCC_DebugLineNumbers>
        <TASM_DisplaySourceLines>true</TASM_DisplaySourceLines>
        <BCC_StackFrames>true</BCC_StackFrames>
        <ILINK_LibraryPath>$(BDS)\lib\debug;$(ILINK_LibraryPath)</ILINK_LibraryPath>
        <ILINK_FullDebugInfo>true</ILINK_FullDebugInfo>
        <TASM_Debugging>Full</TASM_Debugging>
        <BCC_SourceDebuggingOn>true</BCC_SourceDebuggingOn>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1_Win32)'!=''">
        <IncludePath>C:\Program Files (x86)\Winsoft\ComPort for FireMonkey\DelphiXE7-Win32;$(IncludePath)</IncludePath>
        <ILINK_LibraryPath>$(BDS)\lib\win32\release\;C:\Program Files (x86)\Winsoft\ComPort for FireMonkey\DelphiXE7-Win32;D:\Program Files (x86)\Embarcadero\Studio\15.0\lib\win32\release;$(ILINK_LibraryPath)</ILINK_LibraryPath>
        <Defines>_DEBUG;$(Defines)</Defines>
        <ILINK_FullDebugInfo>false</ILINK_FullDebugInfo>
        <LinkPackageStatics>rtl.lib;vcl.lib;fmx.lib;vclimg.lib;xmlrtl.lib;vclactnband.lib;vclx.lib</LinkPackageStatics>
        <DynamicRTL>false</DynamicRTL>
        <UsePackages>false</UsePackages>
        <Manifest_File>$(BDS)\bin\default_app.manifest</Manifest_File>
        <VerInfo_IncludeVerInfo>true</VerInfo_IncludeVerInfo>
        <VerInfo_Locale>1033</VerInfo_Locale>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_1_Win64)'!=''">
        <Defines>_DEBUG;$(Defines)</Defines>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2)'!=''">
        <IntermediateOutputDir>Release</IntermediateOutputDir>
        <ILINK_LibraryPath>$(BDS)\lib\release;$(ILINK_LibraryPath)</ILINK_LibraryPath>
        <TASM_Debugging>None</TASM_Debugging>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2_Win32)'!=''">
        <Defines>NDEBUG;$(Defines)</Defines>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Cfg_2_Win64)'!=''">
        <Defines>NDEBUG;$(Defines)</Defines>
    </PropertyGroup>
    <ItemGroup>
        <LibFiles Include="C:\Program Files (x86)\Winsoft\ComPort\DelphiXE7\Comportp.lib" Condition="'$(Platform)'=='Win32'">
            <BuildOrder>12</BuildOrder>
            <IgnorePath>true</IgnorePath>
        </LibFiles>
        <LibFiles Include="cv.lib">
            <BuildOrder>7</BuildOrder>
            <IgnorePath>true</IgnorePath>
        </LibFiles>
        <LibFiles Include="cvaux.lib">
            <BuildOrder>8</BuildOrder>
            <IgnorePath>true</IgnorePath>
        </LibFiles>
        <LibFiles Include="cvcam.lib">
            <BuildOrder>9</BuildOrder>
            <IgnorePath>true</IgnorePath>
        </LibFiles>
        <LibFiles Include="cxcore.lib">
            <BuildOrder>10</BuildOrder>
            <IgnorePath>true</IgnorePath>
        </LibFiles>
        <LibFiles Include="cxts.lib">
            <BuildOrder>11</BuildOrder>
            <IgnorePath>true</IgnorePath>
        </LibFiles>
        <LibFiles Include="freeimage.lib">
            <BuildOrder>12</BuildOrder>
            <IgnorePath>true</IgnorePath>
        </LibFiles>
        <LibFiles Include="highgui.lib">
            <BuildOrder>13</BuildOrder>
            <IgnorePath>true</IgnorePath>
        </LibFiles>
        <LibFiles Include="ml.lib">
            <BuildOrder>14</BuildOrder>
            <IgnorePath>true</IgnorePath>
        </LibFiles>
        <CppCompile Include="QHY5II_I2C_Explore_1.cpp">
            <BuildOrder>0</BuildOrder>
        </CppCompile>
        <ResFiles Include="QHY5II_I2C_Explore_1.res">
            <BuildOrder>1</BuildOrder>
        </ResFiles>
        <CppCompile Include="qhyccd.cpp">
            <DependentOn>qhyccd.h</DependentOn>
            <BuildOrder>12</BuildOrder>
        </CppCompile>
        <CppCompile Include="Unit1.cpp">
            <Form>Form1</Form>
            <DependentOn>Unit1.h</DependentOn>
            <BuildOrder>2</BuildOrder>
        </CppCompile>
        <FormResources Include="Unit1.dfm"/>
        <BuildConfiguration Include="Release">
            <Key>Cfg_2</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
        <BuildConfiguration Include="Base">
            <Key>Base</Key>
        </BuildConfiguration>
        <BuildConfiguration Include="Debug">
            <Key>Cfg_1</Key>
            <CfgParent>Base</CfgParent>
        </BuildConfiguration>
    </ItemGroup>
    <Import Condition="Exists('$(BDS)\Bin\CodeGear.Cpp.Targets')" Project="$(BDS)\Bin\CodeGear.Cpp.Targets"/>
    <ProjectExtensions>
        <Borland.Personality>CPlusPlusBuilder.Personality.12</Borland.Personality>
        <Borland.ProjectType>CppVCLApplication</Borland.ProjectType>
        <BorlandProject>
            <CPlusPlusBuilder.Personality>
                <Source>
                    <Source Name="MainSource">QHY5II_I2C_Explore_1.cpp</Source>
                </Source>
                <VersionInfo>
                    <VersionInfo Name="IncludeVerInfo">False</VersionInfo>
                    <VersionInfo Name="AutoIncBuild">False</VersionInfo>
                    <VersionInfo Name="MajorVer">1</VersionInfo>
                    <VersionInfo Name="MinorVer">0</VersionInfo>
                    <VersionInfo Name="Release">0</VersionInfo>
                    <VersionInfo Name="Build">0</VersionInfo>
                    <VersionInfo Name="Debug">False</VersionInfo>
                    <VersionInfo Name="PreRelease">False</VersionInfo>
                    <VersionInfo Name="Special">False</VersionInfo>
                    <VersionInfo Name="Private">False</VersionInfo>
                    <VersionInfo Name="DLL">False</VersionInfo>
                    <VersionInfo Name="Locale">2052</VersionInfo>
                    <VersionInfo Name="CodePage">936</VersionInfo>
                </VersionInfo>
                <VersionInfoKeys>
                    <VersionInfoKeys Name="CompanyName"/>
                    <VersionInfoKeys Name="FileDescription"/>
                    <VersionInfoKeys Name="FileVersion">*******</VersionInfoKeys>
                    <VersionInfoKeys Name="InternalName"/>
                    <VersionInfoKeys Name="LegalCopyright"/>
                    <VersionInfoKeys Name="LegalTrademarks"/>
                    <VersionInfoKeys Name="OriginalFilename"/>
                    <VersionInfoKeys Name="ProductName"/>
                    <VersionInfoKeys Name="ProductVersion">*******</VersionInfoKeys>
                    <VersionInfoKeys Name="Comments"/>
                </VersionInfoKeys>
                <Debugging>
                    <Debugging Name="DebugSourceDirs"/>
                </Debugging>
                <Parameters>
                    <Parameters Name="RunParams"/>
                    <Parameters Name="Launcher"/>
                    <Parameters Name="UseLauncher">False</Parameters>
                    <Parameters Name="DebugCWD"/>
                    <Parameters Name="HostApplication"/>
                    <Parameters Name="RemoteHost"/>
                    <Parameters Name="RemotePath"/>
                    <Parameters Name="RemoteParams"/>
                    <Parameters Name="RemoteLauncher"/>
                    <Parameters Name="UseRemoteLauncher">False</Parameters>
                    <Parameters Name="RemoteCWD"/>
                    <Parameters Name="RemoteDebug">False</Parameters>
                    <Parameters Name="Debug Symbols Search Path"/>
                    <Parameters Name="LoadAllSymbols">True</Parameters>
                    <Parameters Name="LoadUnspecifiedSymbols">False</Parameters>
                </Parameters>
                <ProjectProperties>
                    <ProjectProperties Name="AutoShowDeps">False</ProjectProperties>
                    <ProjectProperties Name="ManagePaths">True</ProjectProperties>
                    <ProjectProperties Name="VerifyPackages">True</ProjectProperties>
                    <ProjectProperties Name="IndexFiles">False</ProjectProperties>
                </ProjectProperties>
                <Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\bcboffice2k210.bpl">Embarcadero C++Builder Office 2000 Servers Package</Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\bcbofficexp210.bpl">Embarcadero C++Builder Office XP Servers Package</Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\dcloffice2k210.bpl">Microsoft Office 2000 Sample Automation Server Wrapper Components</Excluded_Packages>
                    <Excluded_Packages Name="$(BDSBIN)\dclofficexp210.bpl">Microsoft Office XP Sample Automation Server Wrapper Components</Excluded_Packages>
                </Excluded_Packages>
            </CPlusPlusBuilder.Personality>
            <Platforms>
                <Platform value="Win32">True</Platform>
                <Platform value="Win64">False</Platform>
            </Platforms>
            <Deployment>
                <DeployFile Condition="'$(UsingDelphiRTL)'=='true'" LocalName="$(BDS)\bin64\borlndmm.dll" Class="DependencyModule">
                    <Platform Name="Win64">
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="cxcore.lib" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="cvcam.lib" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="Debug\QHY5II_I2C_Explore_1.exe" Configuration="Debug" Class="ProjectOutput">
                    <Platform Name="Win32">
                        <RemoteName>QHY5II_I2C_Explore_1.exe</RemoteName>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile Condition="'$(DynamicRTL)'=='true' And '$(Multithreaded)'=='true'" LocalName="$(BDS)\bin\cc32160mt.dll" Class="DependencyModule">
                    <Platform Name="Win32">
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile Condition="'$(DynamicRTL)'=='true'" LocalName="$(BDS)\Redist\osx32\libcgcrtl.dylib" Class="DependencyModule">
                    <Platform Name="OSX32">
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="QHY5II_I2C_Explore_1.res" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="cv.lib" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="C:\Program Files (x86)\Winsoft\ComPort\DelphiXE7\Comportp.lib" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="cxts.lib" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="highgui.lib" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="ml.lib" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile Condition="'$(DynamicRTL)'=='true' And '$(Multithreaded)'=='true'" LocalName="$(BDS)\bin64\cc64160mt.dll" Class="DependencyModule">
                    <Platform Name="Win64">
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="freeimage.lib" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile Condition="'$(DynamicRTL)'=='true'" LocalName="$(BDS)\Redist\osx32\libcgstl.dylib" Class="DependencyModule">
                    <Platform Name="OSX32">
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile Condition="'$(UsingDelphiRTL)'=='true'" LocalName="$(BDS)\bin\borlndmm.dll" Class="DependencyModule">
                    <Platform Name="Win32">
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile LocalName="cvaux.lib" Configuration="Debug" Class="ProjectFile">
                    <Platform Name="Win32">
                        <RemoteDir>.\</RemoteDir>
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile Condition="'$(DynamicRTL)'=='true' And '$(Multithreaded)'!='true'" LocalName="$(BDS)\bin\cc32160.dll" Class="DependencyModule">
                    <Platform Name="Win32">
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployFile Condition="'$(DynamicRTL)'=='true' And '$(Multithreaded)'!='true'" LocalName="$(BDS)\bin64\cc64160.dll" Class="DependencyModule">
                    <Platform Name="Win64">
                        <Overwrite>true</Overwrite>
                    </Platform>
                </DeployFile>
                <DeployClass Required="true" Name="DependencyPackage">
                    <Platform Name="iOSDevice">
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                        <Extensions>.bpl</Extensions>
                    </Platform>
                    <Platform Name="OSX32">
                        <RemoteDir>Contents\MacOS</RemoteDir>
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                    <Platform Name="iOSSimulator">
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                </DeployClass>
                <DeployClass Name="DependencyModule">
                    <Platform Name="iOSDevice">
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                        <Extensions>.dll;.bpl</Extensions>
                    </Platform>
                    <Platform Name="OSX32">
                        <RemoteDir>Contents\MacOS</RemoteDir>
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                    <Platform Name="iOSSimulator">
                        <Operation>1</Operation>
                        <Extensions>.dylib</Extensions>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPad_Launch2048">
                    <Platform Name="iOSDevice">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimulator">
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectOSXInfoPList">
                    <Platform Name="OSX32">
                        <RemoteDir>Contents</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectiOSDeviceDebug">
                    <Platform Name="iOSDevice">
                        <RemoteDir>..\$(PROJECTNAME).app.dSYM\Contents\Resources\DWARF</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_SplashImage470">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-normal</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidLibnativeX86File">
                    <Platform Name="Android">
                        <RemoteDir>library\lib\x86</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectiOSResource">
                    <Platform Name="iOSDevice">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimulator">
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectOSXEntitlements">
                    <Platform Name="OSX32">
                        <RemoteDir>../</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidGDBServer">
                    <Platform Name="Android">
                        <RemoteDir>library\lib\armeabi-v7a</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_Launch640">
                    <Platform Name="iOSDevice">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimulator">
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_SplashImage960">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-xlarge</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_LauncherIcon96">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-xhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_Launch320">
                    <Platform Name="iOSDevice">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimulator">
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_LauncherIcon144">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-xxhdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidLibnativeMipsFile">
                    <Platform Name="Android">
                        <RemoteDir>library\lib\mips</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidSplashImageDef">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="DebugSymbols">
                    <Platform Name="OSX32">
                        <RemoteDir>Contents\MacOS</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimulator">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="DependencyFramework">
                    <Platform Name="OSX32">
                        <RemoteDir>Contents\MacOS</RemoteDir>
                        <Operation>1</Operation>
                        <Extensions>.framework</Extensions>
                    </Platform>
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_SplashImage426">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-small</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectiOSEntitlements">
                    <Platform Name="iOSDevice">
                        <RemoteDir>../</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AdditionalDebugSymbols">
                    <Platform Name="OSX32">
                        <RemoteDir>Contents\MacOS</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimulator">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Win32">
                        <RemoteDir>Contents\MacOS</RemoteDir>
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidClassesDexFile">
                    <Platform Name="Android">
                        <RemoteDir>classes</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectiOSInfoPList">
                    <Platform Name="iOSDevice">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimulator">
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPad_Launch1024">
                    <Platform Name="iOSDevice">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimulator">
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_DefaultAppIcon">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectOSXResource">
                    <Platform Name="OSX32">
                        <RemoteDir>Contents\Resources</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectiOSDeviceResourceRules">
                    <Platform Name="iOSDevice">
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPad_Launch768">
                    <Platform Name="iOSDevice">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimulator">
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Required="true" Name="ProjectOutput">
                    <Platform Name="iOSDevice">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Android">
                        <RemoteDir>library\lib\armeabi-v7a</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                    <Platform Name="OSX32">
                        <RemoteDir>Contents\MacOS</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimulator">
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidLibnativeArmeabiFile">
                    <Platform Name="Android">
                        <RemoteDir>library\lib\armeabi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_SplashImage640">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-large</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="File">
                    <Platform Name="iOSDevice">
                        <Operation>0</Operation>
                    </Platform>
                    <Platform Name="Android">
                        <Operation>0</Operation>
                    </Platform>
                    <Platform Name="Win32">
                        <Operation>0</Operation>
                    </Platform>
                    <Platform Name="OSX32">
                        <RemoteDir>Contents\MacOS</RemoteDir>
                        <Operation>0</Operation>
                    </Platform>
                    <Platform Name="iOSSimulator">
                        <Operation>0</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPhone_Launch640x1136">
                    <Platform Name="iOSDevice">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimulator">
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_LauncherIcon36">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-ldpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="AndroidSplashStyles">
                    <Platform Name="Android">
                        <RemoteDir>res\values</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="iPad_Launch1536">
                    <Platform Name="iOSDevice">
                        <Operation>1</Operation>
                    </Platform>
                    <Platform Name="iOSSimulator">
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_LauncherIcon48">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-mdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="Android_LauncherIcon72">
                    <Platform Name="Android">
                        <RemoteDir>res\drawable-hdpi</RemoteDir>
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <DeployClass Name="ProjectAndroidManifest">
                    <Platform Name="Android">
                        <Operation>1</Operation>
                    </Platform>
                </DeployClass>
                <ProjectRoot Platform="Android" Name="$(PROJECTNAME)"/>
                <ProjectRoot Platform="iOSDevice" Name="$(PROJECTNAME).app"/>
                <ProjectRoot Platform="Win32" Name="$(PROJECTNAME)"/>
                <ProjectRoot Platform="OSX32" Name="$(PROJECTNAME).app"/>
                <ProjectRoot Platform="iOSSimulator" Name="$(PROJECTNAME).app"/>
                <ProjectRoot Platform="Win64" Name="$(PROJECTNAME)"/>
            </Deployment>
        </BorlandProject>
        <ProjectFileVersion>12</ProjectFileVersion>
    </ProjectExtensions>
    <Import Condition="Exists('$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj')" Project="$(APPDATA)\Embarcadero\$(BDSAPPDATABASEDIR)\$(PRODUCTVERSION)\UserTools.proj"/>
    <Import Project="$(MSBuildProjectName).deployproj" Condition="Exists('$(MSBuildProjectName).deployproj')"/>
</Project>
