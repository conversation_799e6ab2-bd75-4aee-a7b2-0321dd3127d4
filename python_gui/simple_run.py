#!/usr/bin/env python3
"""
Simple launcher for QHYCCD Test Application
Bypasses dependency checking and runs the application directly
"""

import sys
import os

def main():
    """Simple main function to run the application"""
    print("QHYCCD Sensor Performance Test Tools")
    print("=" * 40)
    
    # Add current directory to Python path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    try:
        # Try to import and run the main application
        print("Loading application...")
        from main import QHYCCDTestApp
        
        print("Starting GUI...")
        app = QHYCCDTestApp()
        app.run()
        
    except ImportError as e:
        print(f"Import Error: {e}")
        print("\nMissing dependencies. Please install:")
        print("pip install numpy opencv-python Pillow pyserial matplotlib")
        input("Press Enter to exit...")
        return 1
    
    except Exception as e:
        print(f"Application Error: {e}")
        print("\nPlease check the error message above.")
        input("Press Enter to exit...")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
