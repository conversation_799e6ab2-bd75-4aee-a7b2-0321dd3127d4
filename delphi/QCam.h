// QCam.h

#ifndef _QCAM_H_
#define _QCAM_H_

#ifdef __cplusplus 
extern "C" {
#endif	

void __stdcall QCamInit(HANDLE* hCamList, int* pCamCount);
int __stdcall QCamGetType(HANDLE hCam);
BOOL __stdcall QCamIsCmos(HANDLE hCam);
BOOL __stdcall QCamGetFrameSize(HANDLE hCam, int* pW, int* pH);
int __stdcall QCamGetMaxFrameLength(HANDLE hCam);

void __stdcall QCamSetTransferSpeed(HANDLE hCam, unsigned char transferSpeed);
void __stdcall QCamSetExposeTime(HANDLE hCam, double time);
void __stdcall QCamSetGain(HANDLE hCam, unsigned short gain);
BOOL __stdcall QCamBeginLive(HANDLE hCam);
BOOL __stdcall QCamGetLiveFrame(HANDLE hCam, void* data, int* pW, int* pH, int* pBpp, int* lvlStat, int* lvlStatR, int* lvlStatG, int* lvlStatB);
BOOL __stdcall QCamStopLive(HANDLE hCam);

BOOL __stdcall QDbgVendorReqRead(HANDLE hCam, unsigned char code, unsigned short value, unsigned short index, unsigned char* data, long len);
BOOL __stdcall QDbgVendorReqWrite(HANDLE hCam, unsigned char code, unsigned short value, unsigned short index, unsigned char* data, long len);
unsigned short __stdcall QDbgI2CRead(HANDLE hCam, unsigned char regIndex);
BOOL __stdcall QDbgI2CWrite(HANDLE hCam, unsigned char regIndex, unsigned short value);
BOOL __stdcall QDbgI2CBatchRead(HANDLE hCam, unsigned short regIndex, unsigned char* data, unsigned short len);
BOOL __stdcall QDbgI2CBatchWrite(HANDLE hCam, unsigned short regIndex, unsigned char* data, unsigned short len);
BOOL __stdcall QDbgGetFirmwareVersion(HANDLE hCam, char* ver);
BOOL __stdcall QDbgEepromRead(HANDLE hCam, unsigned char addr, unsigned char* data, unsigned short len);
BOOL __stdcall QDbgEepromWrite(HANDLE hCam, unsigned char addr, unsigned char* data, unsigned short len);
void __stdcall QDbgSetRawDataLength(HANDLE hCam, int len);
BOOL __stdcall QDbgBeginLive(HANDLE hCam);
BOOL __stdcall QDbgGetRawData(HANDLE hCam, void* data, int* pLen);
BOOL __stdcall QDbgStopLive(HANDLE hCam);

#ifdef __cplusplus 
}
#endif

#endif
