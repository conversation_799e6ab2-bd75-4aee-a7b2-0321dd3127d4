// QUsb.h

#ifndef _QUSB_H_
#define _QUSB_H_

#ifdef __cplusplus 
extern "C" {
#endif	

void __cdecl QUsbEnumCam(HANDLE* hCamList, int* pCamCount);
int __cdecl QUsbGetCamType(HANDLE hCam);
void __cdecl QUsbGetCamSubtype(HANDLE hCam);
void __cdecl QUsbSetCamParams(HANDLE hCam, unsigned char mode, unsigned char hBin, unsigned char vBin,
				  int sfLeft, int sfTop, int sfWidth, int sfHeight, unsigned char depth, bool live);
void __cdecl QUsbSetTransferSpeed(HANDLE hCam, unsigned char transferSpeed);
void __cdecl QUsbSetExposeTime(HANDLE hCam, unsigned short paramLive, unsigned short paramLong);
bool __cdecl QUsbSetExposeTimeInt(HANDLE hCam, unsigned short paramLive, unsigned short paramLong);
void __cdecl QUsbSetGain(HANDLE hCam, unsigned short param);
bool __cdecl QUsbSetGainInt(HANDLE hCam, unsigned short param);
void __cdecl QUsbBeginTransfer(HANDLE hCam);
void __cdecl QUsbStopTransfer(HANDLE hCam);
void __cdecl QUsbReadRawData(HANDLE hCam, unsigned char* data, long len);
void __cdecl QUsbBeginReadRawData(HANDLE hCam);
int __cdecl QUsbWaitForReadTrans(HANDLE hCam, unsigned char** pData, long* pLen);
void __cdecl QUsbResumeReadTrans(HANDLE hCam, int transIndex);
void __cdecl QUsbFinishReadRawData(HANDLE hCam);
void __cdecl QUsbAbortRead(HANDLE hCam);
void __cdecl QUsbLimitFrameRate(HANDLE hCam, int level);

bool __cdecl QUsbVendorReqRead(HANDLE hCam, unsigned char code, unsigned short value, unsigned short index, unsigned char* data, long len);
bool __cdecl QUsbVendorReqWrite(HANDLE hCam, unsigned char code, unsigned short value, unsigned short index, unsigned char* data, long len);
unsigned short __cdecl QUsbI2CRead(HANDLE hCam, unsigned char regIndex);
bool __cdecl QUsbI2CWrite(HANDLE hCam, unsigned char regIndex, unsigned short value);
bool __cdecl QUsbI2CBatchRead(HANDLE hCam, unsigned short regIndex, unsigned char* data, unsigned short len);
bool __cdecl QUsbI2CBatchWrite(HANDLE hCam, unsigned short regIndex, unsigned char* data, unsigned short len);
bool __cdecl QUsbGetFirmwareVersion(HANDLE hCam, char* ver);
bool __cdecl QUsbEepromRead(HANDLE hCam, unsigned char addr, unsigned char* data, unsigned short len);
bool __cdecl QUsbEepromWrite(HANDLE hCam, unsigned char addr, unsigned char* data, unsigned short len);

#ifdef __cplusplus 
}
#endif

#endif
