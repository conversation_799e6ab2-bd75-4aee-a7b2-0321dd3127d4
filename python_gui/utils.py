"""
Utility functions for QHYCCD camera control application
"""

def hex_to_int(hex_string):
    """Convert hex string to integer, similar to hextoi in Delphi code"""
    try:
        if hex_string.startswith('0x') or hex_string.startswith('0X'):
            return int(hex_string, 16)
        else:
            return int(hex_string, 16)
    except ValueError:
        return 0

def int_to_hex(value, width=4):
    """Convert integer to hex string with specified width"""
    return f"{value:0{width}X}"

def msb(value):
    """Get most significant byte (upper 8 bits)"""
    return (value & 0xFF00) >> 8

def lsb(value):
    """Get least significant byte (lower 8 bits)"""
    return value & 0x00FF

def clamp(value, min_val, max_val):
    """Clamp value between min and max"""
    return max(min_val, min(value, max_val))

def format_register_value(value):
    """Format register value for display"""
    return f"0x{value:04X}"

class BitManipulator:
    """Helper class for bit manipulation operations"""
    
    def __init__(self):
        self.bits = [False] * 16  # 16-bit register
    
    def set_bit(self, position, value):
        """Set bit at position to value (True/False)"""
        if 0 <= position < 16:
            self.bits[position] = value
    
    def get_bit(self, position):
        """Get bit value at position"""
        if 0 <= position < 16:
            return self.bits[position]
        return False
    
    def to_int(self):
        """Convert bit array to integer"""
        result = 0
        for i, bit in enumerate(self.bits):
            if bit:
                result |= (1 << i)
        return result
    
    def from_int(self, value):
        """Set bits from integer value"""
        for i in range(16):
            self.bits[i] = bool(value & (1 << i))

def calculate_rms(image_data):
    """Calculate RMS value of image data"""
    import numpy as np
    if image_data is None or len(image_data) == 0:
        return 0.0
    return np.sqrt(np.mean(np.square(image_data)))

def calculate_std(image_data):
    """Calculate standard deviation of image data"""
    import numpy as np
    if image_data is None or len(image_data) == 0:
        return 0.0
    return np.std(image_data)

def calculate_mean(image_data):
    """Calculate mean value of image data"""
    import numpy as np
    if image_data is None or len(image_data) == 0:
        return 0.0
    return np.mean(image_data)

class ExposureTimeCalculator:
    """Helper class for automatic exposure time calculation"""
    
    def __init__(self):
        self.target_rms = 30000
        self.max_rms = 60000
        self.min_rms = 25000
    
    def calculate_new_exposure(self, current_exp, current_rms):
        """Calculate new exposure time based on current RMS"""
        if current_rms > self.max_rms:
            return current_exp // 2
        elif current_rms > self.target_rms:
            return int(current_exp * self.target_rms / current_rms)
        elif current_rms < self.min_rms and current_rms > 0:
            return int(current_exp * self.target_rms / current_rms)
        return current_exp

def validate_com_port(port_name):
    """Validate COM port name format"""
    import re
    pattern = r'^COM\d+$'
    return bool(re.match(pattern, port_name.upper()))

def get_available_com_ports():
    """Get list of available COM ports"""
    import serial.tools.list_ports
    ports = serial.tools.list_ports.comports()
    return [port.device for port in ports]
