"""
Image Processing and Analysis Module
Handles image processing, analysis, and display functions
"""

import numpy as np
import cv2
from PIL import Image, ImageTk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import logging
from typing import Tuple, Optional, Dict, Any

class ImageProcessor:
    """Image processing and analysis class"""
    
    def __init__(self):
        self.current_image = None
        self.flat_images = []
        self.bias_images = []
        self.preview_size = (352, 201)  # Size from Delphi form
        
    def process_raw_image(self, raw_data: np.ndarray, bit_depth: int = 16) -> np.ndarray:
        """Process raw camera data to displayable image"""
        if raw_data is None:
            return None
        
        try:
            # Ensure proper data type
            if bit_depth == 16:
                if raw_data.dtype != np.uint16:
                    raw_data = raw_data.astype(np.uint16)
            else:
                if raw_data.dtype != np.uint8:
                    raw_data = raw_data.astype(np.uint8)
            
            self.current_image = raw_data.copy()
            return raw_data
            
        except Exception as e:
            logging.error(f"Failed to process raw image: {e}")
            return None
    
    def convert_to_display_image(self, image_data: np.ndarray, 
                                target_size: Tuple[int, int] = None) -> Optional[np.ndarray]:
        """Convert image data to displayable format"""
        if image_data is None:
            return None
        
        try:
            # Convert 16-bit to 8-bit for display
            if image_data.dtype == np.uint16:
                # Scale to 8-bit range
                display_image = (image_data >> 8).astype(np.uint8)
            else:
                display_image = image_data.astype(np.uint8)
            
            # Convert grayscale to RGB if needed
            if len(display_image.shape) == 2:
                display_image = cv2.cvtColor(display_image, cv2.COLOR_GRAY2RGB)
            
            # Resize if target size specified
            if target_size:
                display_image = cv2.resize(display_image, target_size)
            
            return display_image
            
        except Exception as e:
            logging.error(f"Failed to convert to display image: {e}")
            return None
    
    def create_tkinter_image(self, image_data: np.ndarray, 
                           target_size: Tuple[int, int] = None) -> Optional[ImageTk.PhotoImage]:
        """Create tkinter-compatible image"""
        if image_data is None:
            return None
        
        try:
            display_image = self.convert_to_display_image(image_data, target_size)
            if display_image is None:
                return None
            
            # Convert to PIL Image
            pil_image = Image.fromarray(display_image)
            
            # Convert to PhotoImage
            return ImageTk.PhotoImage(pil_image)
            
        except Exception as e:
            logging.error(f"Failed to create tkinter image: {e}")
            return None
    
    def calculate_statistics(self, image_data: np.ndarray, 
                           roi: Tuple[int, int, int, int] = None) -> Dict[str, float]:
        """Calculate image statistics (mean, std, rms)"""
        if image_data is None:
            return {'mean': 0.0, 'std': 0.0, 'rms': 0.0, 'min': 0.0, 'max': 0.0}
        
        try:
            # Apply ROI if specified
            if roi:
                x, y, w, h = roi
                if x >= 0 and y >= 0 and x + w <= image_data.shape[1] and y + h <= image_data.shape[0]:
                    image_data = image_data[y:y+h, x:x+w]
            
            # Calculate statistics
            mean_val = np.mean(image_data)
            std_val = np.std(image_data)
            rms_val = np.sqrt(np.mean(np.square(image_data)))
            min_val = np.min(image_data)
            max_val = np.max(image_data)
            
            return {
                'mean': float(mean_val),
                'std': float(std_val),
                'rms': float(rms_val),
                'min': float(min_val),
                'max': float(max_val)
            }
            
        except Exception as e:
            logging.error(f"Failed to calculate statistics: {e}")
            return {'mean': 0.0, 'std': 0.0, 'rms': 0.0, 'min': 0.0, 'max': 0.0}
    
    def create_histogram(self, image_data: np.ndarray, bins: int = 256) -> Tuple[np.ndarray, np.ndarray]:
        """Create histogram of image data"""
        if image_data is None:
            return np.array([]), np.array([])
        
        try:
            hist, bin_edges = np.histogram(image_data.flatten(), bins=bins)
            return hist, bin_edges
            
        except Exception as e:
            logging.error(f"Failed to create histogram: {e}")
            return np.array([]), np.array([])
    
    def create_histogram_plot(self, image_data: np.ndarray, 
                            figure_size: Tuple[int, int] = (6, 4)) -> Optional[plt.Figure]:
        """Create matplotlib histogram plot"""
        if image_data is None:
            return None
        
        try:
            fig, ax = plt.subplots(figsize=figure_size)
            ax.hist(image_data.flatten(), bins=256, alpha=0.7, color='blue')
            ax.set_xlabel('Pixel Value')
            ax.set_ylabel('Frequency')
            ax.set_title('Image Histogram')
            ax.grid(True, alpha=0.3)
            
            return fig
            
        except Exception as e:
            logging.error(f"Failed to create histogram plot: {e}")
            return None
    
    def subtract_bias(self, image_data: np.ndarray, bias_data: np.ndarray) -> Optional[np.ndarray]:
        """Subtract bias frame from image"""
        if image_data is None or bias_data is None:
            return image_data
        
        try:
            # Ensure same dimensions
            if image_data.shape != bias_data.shape:
                logging.warning("Image and bias frame dimensions don't match")
                return image_data
            
            # Subtract bias (with clipping to prevent underflow)
            result = np.clip(image_data.astype(np.int32) - bias_data.astype(np.int32), 
                           0, np.iinfo(image_data.dtype).max)
            
            return result.astype(image_data.dtype)
            
        except Exception as e:
            logging.error(f"Failed to subtract bias: {e}")
            return image_data
    
    def divide_by_flat(self, image_data: np.ndarray, flat_data: np.ndarray, 
                      normalize_value: float = None) -> Optional[np.ndarray]:
        """Divide image by flat field"""
        if image_data is None or flat_data is None:
            return image_data
        
        try:
            # Ensure same dimensions
            if image_data.shape != flat_data.shape:
                logging.warning("Image and flat frame dimensions don't match")
                return image_data
            
            # Normalize flat field
            if normalize_value is None:
                normalize_value = np.mean(flat_data)
            
            # Avoid division by zero
            flat_normalized = np.where(flat_data > 0, flat_data / normalize_value, 1.0)
            
            # Divide image by normalized flat
            result = np.clip(image_data.astype(np.float32) / flat_normalized, 
                           0, np.iinfo(image_data.dtype).max)
            
            return result.astype(image_data.dtype)
            
        except Exception as e:
            logging.error(f"Failed to divide by flat: {e}")
            return image_data
    
    def calculate_noise_metrics(self, image_data: np.ndarray, 
                              bias_frames: list = None) -> Dict[str, float]:
        """Calculate noise metrics including readout noise"""
        if image_data is None:
            return {'readout_noise': 0.0, 'total_noise': 0.0}
        
        try:
            metrics = {}
            
            # Calculate total noise (standard deviation)
            metrics['total_noise'] = float(np.std(image_data))
            
            # Calculate readout noise if bias frames available
            if bias_frames and len(bias_frames) >= 2:
                # Use difference method for readout noise
                diff_image = bias_frames[0].astype(np.float32) - bias_frames[1].astype(np.float32)
                readout_noise = np.std(diff_image) / np.sqrt(2)
                metrics['readout_noise'] = float(readout_noise)
            else:
                # Estimate from single frame (less accurate)
                metrics['readout_noise'] = metrics['total_noise']
            
            return metrics
            
        except Exception as e:
            logging.error(f"Failed to calculate noise metrics: {e}")
            return {'readout_noise': 0.0, 'total_noise': 0.0}
    
    def calculate_gain_metrics(self, flat_frames: list, bias_frames: list = None) -> Dict[str, float]:
        """Calculate gain and full well capacity"""
        if not flat_frames or len(flat_frames) < 2:
            return {'gain': 0.0, 'full_well': 0.0}
        
        try:
            metrics = {}
            
            # Calculate gain using photon transfer curve method
            # Gain = (mean_signal^2) / (variance_signal - variance_bias)
            
            flat1 = flat_frames[0].astype(np.float32)
            flat2 = flat_frames[1].astype(np.float32)
            
            # Calculate mean signal
            mean_signal = (np.mean(flat1) + np.mean(flat2)) / 2.0
            
            # Calculate signal variance
            signal_diff = flat1 - flat2
            signal_variance = np.var(signal_diff) / 2.0
            
            # Subtract bias variance if available
            bias_variance = 0.0
            if bias_frames and len(bias_frames) >= 2:
                bias1 = bias_frames[0].astype(np.float32)
                bias2 = bias_frames[1].astype(np.float32)
                bias_diff = bias1 - bias2
                bias_variance = np.var(bias_diff) / 2.0
            
            # Calculate gain (e-/ADU)
            net_variance = signal_variance - bias_variance
            if net_variance > 0 and mean_signal > 0:
                gain = (mean_signal * mean_signal) / net_variance
                metrics['gain'] = float(gain)
                
                # Calculate full well capacity
                max_signal = np.max([np.max(flat1), np.max(flat2)])
                full_well = gain * max_signal
                metrics['full_well'] = float(full_well)
            else:
                metrics['gain'] = 0.0
                metrics['full_well'] = 0.0
            
            return metrics
            
        except Exception as e:
            logging.error(f"Failed to calculate gain metrics: {e}")
            return {'gain': 0.0, 'full_well': 0.0}
    
    def save_image(self, image_data: np.ndarray, filename: str) -> bool:
        """Save image to file"""
        if image_data is None:
            return False
        
        try:
            if image_data.dtype == np.uint16:
                # Save as 16-bit TIFF
                cv2.imwrite(filename, image_data)
            else:
                # Save as 8-bit
                cv2.imwrite(filename, image_data)
            
            return True
            
        except Exception as e:
            logging.error(f"Failed to save image: {e}")
            return False
    
    def load_image(self, filename: str) -> Optional[np.ndarray]:
        """Load image from file"""
        try:
            image_data = cv2.imread(filename, cv2.IMREAD_UNCHANGED)
            return image_data
            
        except Exception as e:
            logging.error(f"Failed to load image: {e}")
            return None
