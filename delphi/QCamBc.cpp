#include <windows.h>

#include "QCamBc.h"

FuncQCamInit QCamInit;
FuncQCamGetCamType QCamGetCamType;
FuncQCamGetFrameSize QCamGetFrameSize;
FuncQCamGetMaxFrameLength QCamGetMaxFrameLength;
FuncQCamSetBinning QCamSetBinning;
FuncQCamSetResolution QCamSetResolution;
FuncQCamSetDepth QCamSetDepth;
FuncQCamSetTransferSpeed QCamSetTransferSpeed;
FuncQCamSetUsbTraffic QCamSetUsbTraffic;
FuncQCamSetExposeTime QCamSetExposeTime;
FuncQCamSetGain QCamSetGain;
FuncQCamSetGainRgb QCamSetGainRgb;
FuncQCamBeginLive QCamBeginLive;
FuncQCamBeginSingleExp QCamBeginSingleExp;
FuncQCamGetLiveFrame QCamGetLiveFrame;
FuncQCamGetSingleFrame QCamGetSingleFrame;
FuncQCamStopLive QCamStopLive;
FuncQCamStopSingleExp QCamStopSingleExp;
FuncQCamSetRaw QCamSetRaw;
FuncQCamSetDarkFrameSub QCamSetDarkFrameSub;
FuncQCamSetDarkFrame QCamSetDarkFrame;
FuncQCamSetSoftwareBinning QCamSetSoftwareBinning;

FuncQHY5IISetLineNoiseRmv QHY5IISetLineNoiseRmv;

FuncQHY5LIISetHighGainBoost QHY5LIISetHighGainBoost;

FuncQDbgVendorReqRead QDbgVendorReqRead;
FuncQDbgVendorReqWrite QDbgVendorReqWrite;
FuncQDbgInterruptWrite QDbgInterruptWrite;
FuncQDbgInterruptWriteEx QDbgInterruptWriteEx;
FuncQDbgInterruptRead QDbgInterruptRead;
FuncQDbgInterruptReadEx QDbgInterruptReadEx;
FuncQDbgI2CRead QDbgI2CRead;
FuncQDbgI2CCacheRead QDbgI2CCacheRead;
FuncQDbgI2CWrite QDbgI2CWrite;
FuncQDbgI2CBatchRead QDbgI2CBatchRead;
FuncQDbgI2CCacheBatchRead QDbgI2CCacheBatchRead;
FuncQDbgI2CBatchWrite QDbgI2CBatchWrite;
FuncQDbgGetFirmwareVersion QDbgGetFirmwareVersion;
FuncQDbgEepromRead QDbgEepromRead;
FuncQDbgEepromWrite QDbgEepromWrite;
FuncQDbgSetFinetune QDbgSetFinetune;
FuncQDbgSetUsbTxSize QDbgSetUsbTxSize;
FuncQDbgBeginLive QDbgBeginLive;
FuncQDbgGetRawData QDbgGetRawData;
FuncQDbgStopLive QDbgStopLive;

HMODULE hDll;

void QCamImport()
{
    hDll = ::LoadLibrary("QCam.dll");
	QCamInit = (FuncQCamInit)::GetProcAddress(hDll, "QCamInit");
	QCamGetCamType = (FuncQCamGetCamType)::GetProcAddress(hDll, "QCamGetCamType");
	QCamGetFrameSize = (FuncQCamGetFrameSize)::GetProcAddress(hDll, "QCamGetFrameSize");
	QCamGetMaxFrameLength = (FuncQCamGetMaxFrameLength)::GetProcAddress(hDll, "QCamGetMaxFrameLength");
	QCamSetBinning = (FuncQCamSetBinning)::GetProcAddress(hDll, "QCamSetBinning");
	QCamSetResolution = (FuncQCamSetResolution)::GetProcAddress(hDll, "QCamSetResolution");
	QCamSetDepth = (FuncQCamSetDepth)::GetProcAddress(hDll, "QCamSetDepth");
	QCamSetTransferSpeed = (FuncQCamSetTransferSpeed)::GetProcAddress(hDll, "QCamSetTransferSpeed");
	QCamSetUsbTraffic = (FuncQCamSetUsbTraffic)::GetProcAddress(hDll, "QCamSetUsbTraffic");
	QCamSetExposeTime = (FuncQCamSetExposeTime)::GetProcAddress(hDll, "QCamSetExposeTime");
	QCamSetGain = (FuncQCamSetGain)::GetProcAddress(hDll, "QCamSetGain");
	QCamSetGainRgb = (FuncQCamSetGainRgb)::GetProcAddress(hDll, "QCamSetGainRgb");
	QCamBeginLive = (FuncQCamBeginLive)::GetProcAddress(hDll, "QCamBeginLive");
	QCamBeginSingleExp = (FuncQCamBeginSingleExp)::GetProcAddress(hDll, "QCamBeginSingleExp");
	QCamGetLiveFrame = (FuncQCamGetLiveFrame)::GetProcAddress(hDll, "QCamGetLiveFrame");
	QCamGetSingleFrame = (FuncQCamGetSingleFrame)::GetProcAddress(hDll, "QCamGetSingleFrame");
	QCamStopLive = (FuncQCamStopLive)::GetProcAddress(hDll, "QCamStopLive");
	QCamStopSingleExp = (FuncQCamStopSingleExp)::GetProcAddress(hDll, "QCamStopSingleExp");
	QCamSetRaw = (FuncQCamSetRaw)::GetProcAddress(hDll, "QCamSetRaw");
	QCamSetDarkFrameSub = (FuncQCamSetDarkFrameSub)::GetProcAddress(hDll, "QCamSetDarkFrameSub");
	QCamSetDarkFrame = (FuncQCamSetDarkFrame)::GetProcAddress(hDll, "QCamSetDarkFrame");
	QCamSetSoftwareBinning = (FuncQCamSetSoftwareBinning)::GetProcAddress(hDll, "QCamSetSoftwareBinning");

	QHY5IISetLineNoiseRmv = (FuncQHY5IISetLineNoiseRmv)::GetProcAddress(hDll, "QHY5IISetLineNoiseRmv");

	QHY5LIISetHighGainBoost = (FuncQHY5LIISetHighGainBoost)::GetProcAddress(hDll, "QHY5LIISetHighGainBoost");

	QDbgVendorReqRead = (FuncQDbgVendorReqRead)::GetProcAddress(hDll, "QDbgVendorReqRead");
	QDbgVendorReqWrite = (FuncQDbgVendorReqWrite)::GetProcAddress(hDll, "QDbgVendorReqWrite");
	QDbgInterruptWrite = (FuncQDbgInterruptWrite)::GetProcAddress(hDll, "QDbgInterruptWrite");
	QDbgInterruptWriteEx = (FuncQDbgInterruptWriteEx)::GetProcAddress(hDll, "QDbgInterruptWriteEx");
	QDbgInterruptRead = (FuncQDbgInterruptRead)::GetProcAddress(hDll, "QDbgInterruptRead");
	QDbgInterruptReadEx = (FuncQDbgInterruptReadEx)::GetProcAddress(hDll, "QDbgInterruptReadEx");
	QDbgI2CRead = (FuncQDbgI2CRead)::GetProcAddress(hDll, "QDbgI2CRead");
	QDbgI2CCacheRead = (FuncQDbgI2CCacheRead)::GetProcAddress(hDll, "QDbgI2CCacheRead");
	QDbgI2CWrite = (FuncQDbgI2CWrite)::GetProcAddress(hDll, "QDbgI2CWrite");
	QDbgI2CBatchRead = (FuncQDbgI2CBatchRead)::GetProcAddress(hDll, "QDbgI2CBatchRead");
	QDbgI2CCacheBatchRead = (FuncQDbgI2CCacheBatchRead)::GetProcAddress(hDll, "QDbgI2CCacheBatchRead");
	QDbgI2CBatchWrite = (FuncQDbgI2CBatchWrite)::GetProcAddress(hDll, "QDbgI2CBatchWrite");
	QDbgGetFirmwareVersion = (FuncQDbgGetFirmwareVersion)::GetProcAddress(hDll, "QDbgGetFirmwareVersion");
	QDbgEepromRead = (FuncQDbgEepromRead)::GetProcAddress(hDll, "QDbgEepromRead");
	QDbgEepromWrite = (FuncQDbgEepromWrite)::GetProcAddress(hDll, "QDbgEepromWrite");
	QDbgSetFinetune = (FuncQDbgSetFinetune)::GetProcAddress(hDll, "QDbgSetFinetune");
	QDbgBeginLive = (FuncQDbgBeginLive)::GetProcAddress(hDll, "QDbgBeginLive");
	QDbgSetUsbTxSize = (FuncQDbgSetUsbTxSize)::GetProcAddress(hDll, "QDbgSetUsbTxSize");
	QDbgGetRawData = (FuncQDbgGetRawData)::GetProcAddress(hDll, "QDbgGetRawData");
	QDbgStopLive = (FuncQDbgStopLive)::GetProcAddress(hDll, "QDbgStopLive");
}


void QCamFree()
{
	::FreeLibrary(hDll);
}
