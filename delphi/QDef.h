//// QDef.h
//
//#ifndef _QDEF_H_
//#define _QDEF_H_
//
//#ifdef __cplusplus 
//extern "C" {
//#endif	

#define QCAM_MAXCAMERACOUNT 16

#define QCAM_IOTYPE_USB 10
#define QCAM_IOTYPE_ETHERNET 20

#define QCAM_CAMTYPE_UNKNOWN -1
#define QCAM_CAMTYPE_QHY5II 350
#define QCAM_CAMTYPE_QHY5TII 351

#define QCAM_MODE_FOCUS 10
#define QCAM_MODE_PREVIEW 20
#define QCAM_MODE_FULL 30

#define QCAM_DEPTH_BIT8 10
#define QCAM_DEPTH_BIT16 20

#define QCAM_TRANSFERSPEED_HIGH 10
#define QCAM_TRANSFERSPEED_MEDIUM 20
#define QCAM_TRANSFERSPEED_LOW 30

#define QCAM_SCENE_AUTO 10
#define QCAM_SCENE_MICROSCOPE 20
#define QCAM_SCENE_FLUORESCENCE 30

#define QCAM_AWBMODE_GLOBAL 10
#define QCAM_AWBMODE_SP 20

#define QCAM_DEMOSAICMODE_NONE 0

#define QCAM_MAXFRAMERATELEVEL 20

//#ifdef __cplusplus 
//}
//#endif
//
//#endif
