#!/usr/bin/env python3
"""
QHYCCD DLL Test Script
Test loading and basic functionality of qhyccd.dll
"""

import sys
import os
import ctypes
import platform
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_dll_loading():
    """Test different methods of loading QHYCCD DLL"""
    print("QHYCCD DLL Loading Test")
    print("=" * 40)
    print(f"Python version: {sys.version}")
    print(f"Python architecture: {platform.architecture()[0]}")
    print(f"Platform: {sys.platform}")
    print()
    
    # List of DLL candidates to try
    current_dir = os.path.dirname(__file__)
    dll_candidates = [
        # Current directory
        os.path.join(current_dir, 'qhyccd.dll'),
        # Delphi directory
        os.path.join(current_dir, '..', 'delphi', 'qhyccd.dll'),
        # 64-bit specific
        os.path.join(current_dir, '..', 'delphi', 'qhyccd294-64.dll'),
        # 32-bit specific  
        os.path.join(current_dir, '..', 'delphi', 'qhyccd294-32.dll'),
        # System path
        'qhyccd.dll'
    ]
    
    print("Checking DLL candidates:")
    for dll_path in dll_candidates:
        if dll_path != 'qhyccd.dll':
            exists = os.path.exists(dll_path)
            print(f"  {dll_path}: {'EXISTS' if exists else 'NOT FOUND'}")
            if exists:
                try:
                    size = os.path.getsize(dll_path)
                    print(f"    Size: {size:,} bytes")
                except:
                    pass
        else:
            print(f"  {dll_path}: SYSTEM PATH")
    
    print()
    print("Testing DLL loading methods:")
    
    # Test each candidate
    for i, dll_path in enumerate(dll_candidates, 1):
        print(f"\n{i}. Testing: {dll_path}")
        
        if dll_path != 'qhyccd.dll' and not os.path.exists(dll_path):
            print("   SKIP - File not found")
            continue
        
        # Test CDLL
        try:
            print("   Trying ctypes.CDLL...")
            dll = ctypes.CDLL(dll_path)
            print("   ✓ CDLL loaded successfully")
            
            # Test if it has QHYCCD functions
            if hasattr(dll, 'InitQHYCCDResource'):
                print("   ✓ Has InitQHYCCDResource function")
                
                # Try to call InitQHYCCDResource
                try:
                    dll.InitQHYCCDResource.restype = ctypes.c_uint32
                    result = dll.InitQHYCCDResource()
                    print(f"   ✓ InitQHYCCDResource() returned: {result}")
                    
                    # Try ScanQHYCCD
                    if hasattr(dll, 'ScanQHYCCD'):
                        dll.ScanQHYCCD.restype = ctypes.c_uint32
                        num_cameras = dll.ScanQHYCCD()
                        print(f"   ✓ ScanQHYCCD() found {num_cameras} cameras")
                    
                    print("   ✓ DLL is fully functional!")
                    return dll, dll_path
                    
                except Exception as e:
                    print(f"   ✗ Function call failed: {e}")
            else:
                print("   ✗ Missing InitQHYCCDResource function")
                
        except Exception as e:
            print(f"   ✗ CDLL failed: {e}")
        
        # Test WinDLL (Windows only)
        if sys.platform == 'win32':
            try:
                print("   Trying ctypes.WinDLL...")
                dll = ctypes.WinDLL(dll_path)
                print("   ✓ WinDLL loaded successfully")
                
                if hasattr(dll, 'InitQHYCCDResource'):
                    print("   ✓ Has InitQHYCCDResource function")
                    
                    try:
                        dll.InitQHYCCDResource.restype = ctypes.c_uint32
                        result = dll.InitQHYCCDResource()
                        print(f"   ✓ InitQHYCCDResource() returned: {result}")
                        
                        if hasattr(dll, 'ScanQHYCCD'):
                            dll.ScanQHYCCD.restype = ctypes.c_uint32
                            num_cameras = dll.ScanQHYCCD()
                            print(f"   ✓ ScanQHYCCD() found {num_cameras} cameras")
                        
                        print("   ✓ WinDLL is fully functional!")
                        return dll, dll_path
                        
                    except Exception as e:
                        print(f"   ✗ WinDLL function call failed: {e}")
                else:
                    print("   ✗ WinDLL missing InitQHYCCDResource function")
                    
            except Exception as e:
                print(f"   ✗ WinDLL failed: {e}")
    
    print("\n" + "=" * 40)
    print("❌ No working DLL found!")
    return None, None

def provide_solutions():
    """Provide solutions for common DLL loading issues"""
    print("\n🔧 TROUBLESHOOTING SOLUTIONS:")
    print("=" * 40)
    
    python_arch = platform.architecture()[0]
    print(f"Your Python is {python_arch}")
    
    if python_arch == '64bit':
        print("\n✅ For 64-bit Python, you need:")
        print("   1. 64-bit qhyccd.dll")
        print("   2. Place it in python_gui/ directory")
        print("   3. Or use qhyccd294-64.dll from delphi/ directory")
    else:
        print("\n✅ For 32-bit Python, you need:")
        print("   1. 32-bit qhyccd.dll") 
        print("   2. Place it in python_gui/ directory")
        print("   3. Or use qhyccd294-32.dll from delphi/ directory")
    
    print("\n📁 File locations to check:")
    current_dir = os.path.dirname(__file__)
    print(f"   - {os.path.join(current_dir, 'qhyccd.dll')}")
    print(f"   - {os.path.join(current_dir, '..', 'delphi', 'qhyccd.dll')}")
    print(f"   - {os.path.join(current_dir, '..', 'delphi', 'qhyccd294-64.dll')}")
    
    print("\n🔄 Quick fixes:")
    print("   1. Copy the correct DLL to python_gui/ directory")
    print("   2. Rename qhyccd294-64.dll to qhyccd.dll if needed")
    print("   3. Make sure QHYCCD SDK is properly installed")
    print("   4. Try running as Administrator")

def main():
    """Main test function"""
    dll, dll_path = test_dll_loading()
    
    if dll and dll_path:
        print(f"\n🎉 SUCCESS! Working DLL found: {dll_path}")
        print("The application should work correctly now.")
        
        # Cleanup
        try:
            if hasattr(dll, 'ReleaseQHYCCDResource'):
                dll.ReleaseQHYCCDResource()
        except:
            pass
    else:
        provide_solutions()
    
    print("\n" + "=" * 40)
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
