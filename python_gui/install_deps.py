#!/usr/bin/env python3
"""
Install Dependencies Script
Manually install all required dependencies for QHYCCD application
"""

import sys
import subprocess
import importlib

def install_package(package_name):
    """Install a single package using pip"""
    try:
        print(f"Installing {package_name}...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package_name])
        print(f"✓ {package_name} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install {package_name}: {e}")
        return False

def test_import(package_name, import_name=None):
    """Test if a package can be imported"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✓ {package_name} is working")
        return True
    except ImportError:
        print(f"✗ {package_name} import failed")
        return False

def main():
    """Main installation function"""
    print("QHYCCD Application Dependency Installer")
    print("=" * 45)
    
    # List of packages to install
    packages = [
        ('numpy', 'numpy'),
        ('opencv-python', 'cv2'),
        ('Pillow', 'PIL'),
        ('pyserial', 'serial'),
        ('matplotlib', 'matplotlib'),
    ]
    
    print("Installing required packages...")
    print()
    
    failed_packages = []
    
    for package_name, import_name in packages:
        # Try to install the package
        if install_package(package_name):
            # Test if it can be imported
            if not test_import(package_name, import_name):
                failed_packages.append(package_name)
        else:
            failed_packages.append(package_name)
        print()
    
    # Summary
    print("=" * 45)
    if not failed_packages:
        print("✓ All packages installed successfully!")
        print("You can now run the application with:")
        print("  python simple_run.py")
    else:
        print(f"✗ Failed to install: {', '.join(failed_packages)}")
        print("\nTry installing manually:")
        for package in failed_packages:
            print(f"  pip install {package}")
    
    print()
    input("Press Enter to exit...")
    return 0 if not failed_packages else 1

if __name__ == "__main__":
    sys.exit(main())
