"""
Configuration settings for QHYCCD Test Application
Modify these settings to customize application behavior
"""

# ==================== Camera Settings ====================

# Default camera parameters
DEFAULT_CAMERA_PARAMS = {
    'exposure_time': 45000,      # microseconds
    'gain': 0,                   # camera gain
    'offset': 30,                # camera offset
    'wb_red': 64,               # white balance red
    'wb_green': 64,             # white balance green
    'wb_blue': 64,              # white balance blue
    'usb_traffic': 0,           # USB traffic control
    'transfer_bit': 16,         # transfer bit depth
    'ddr_mode': 0,              # DDR mode (0=off, 1=on)
}

# Camera resolution settings
DEFAULT_RESOLUTION = {
    'start_x': 0,
    'start_y': 0,
    'width': 1000,
    'height': 1000
}

# Live preview settings
LIVE_PREVIEW = {
    'fps': 20,                  # target frames per second
    'update_interval': 0.05,    # seconds between frame updates
    'display_size': (352, 201), # preview display size
}

# ==================== Test Automation Settings ====================

# Default test parameters
TEST_PARAMS = {
    'flat_exposure_time': 45000,    # microseconds
    'bias_exposure_time': 1000,     # microseconds
    'num_flat_frames': 10,          # number of flat frames to capture
    'num_bias_frames': 10,          # number of bias frames to capture
    'target_rms': 30000,            # target RMS for exposure adjustment
    'max_rms': 60000,               # maximum RMS threshold
    'min_rms': 25000,               # minimum RMS threshold
    'roi_x': 400,                   # ROI start X
    'roi_y': 400,                   # ROI start Y
    'roi_width': 500,               # ROI width
    'roi_height': 500,              # ROI height
}

# Gain test settings
GAIN_TEST = {
    'start_gain': 0,                # starting gain value
    'max_gain': 100,                # maximum gain value
    'gain_step': 10,                # gain increment step
    'frames_per_gain': 2,           # frames to capture per gain setting
}

# Noise test settings
NOISE_TEST = {
    'num_bias_frames': 20,          # frames for noise measurement
    'exposure_time': 1000,          # bias exposure time (microseconds)
}

# Full well test settings
FULL_WELL_TEST = {
    'exposure_times': [1000, 5000, 10000, 20000, 50000, 100000, 200000, 500000],  # microseconds
    'saturation_threshold': 0.95,   # fraction of max value for saturation detection
}

# ==================== Serial Communication Settings ====================

# Default serial port settings
SERIAL_CONFIG = {
    'default_port': 'COM3',         # default COM port
    'baud_rate': 9600,              # baud rate
    'data_bits': 8,                 # data bits
    'stop_bits': 1,                 # stop bits
    'parity': 'N',                  # parity (N=None, E=Even, O=Odd)
    'timeout': 1.0,                 # read timeout in seconds
    'write_timeout': 1.0,           # write timeout in seconds
}

# Flat panel commands
FLAT_PANEL_COMMANDS = {
    'on': 'F',                      # command to turn on flat panel
    'off': 'N',                     # command to turn off flat panel
}

# ==================== GUI Settings ====================

# Main window settings
WINDOW_CONFIG = {
    'title': 'QHYCCD Sensor Performance Test Tools',
    'width': 1453,
    'height': 845,
    'resizable': True,
}

# Color scheme
COLORS = {
    'background': '#f0f0f0',
    'canvas_bg': 'black',
    'text_color': 'black',
    'error_color': 'red',
    'success_color': 'green',
    'warning_color': 'orange',
}

# Font settings
FONTS = {
    'default': ('Tahoma', 9),
    'large': ('Tahoma', 12),
    'small': ('Tahoma', 8),
    'mono': ('Courier New', 9),
}

# ==================== Logging Settings ====================

# Logging configuration
LOGGING_CONFIG = {
    'level': 'INFO',                # DEBUG, INFO, WARNING, ERROR, CRITICAL
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'max_memo_lines': 1000,         # maximum lines in memo area
}

# ==================== Image Processing Settings ====================

# Image processing parameters
IMAGE_PROCESSING = {
    'histogram_bins': 256,          # number of histogram bins
    'display_bit_depth': 8,         # bit depth for display (8 or 16)
    'auto_scale': True,             # automatically scale images for display
    'gamma_correction': 1.0,        # gamma correction factor
}

# Statistics calculation
STATISTICS = {
    'precision': 2,                 # decimal places for statistics display
    'roi_default': (400, 400, 500, 500),  # default ROI (x, y, width, height)
}

# ==================== File Paths ====================

# File and directory paths
PATHS = {
    'qhyccd_dll': 'qhyccd.dll',     # QHYCCD DLL path (relative or absolute)
    'log_file': 'qhyccd_test.log',  # log file name
    'config_file': 'user_config.ini',  # user configuration file
    'data_dir': 'test_data',        # directory for test data
}

# ==================== Advanced Settings ====================

# Performance settings
PERFORMANCE = {
    'max_threads': 4,               # maximum number of worker threads
    'memory_limit_mb': 1024,        # memory limit for image buffers (MB)
    'cache_size': 10,               # number of images to cache
}

# Debug settings
DEBUG = {
    'enable_debug_mode': False,     # enable debug features
    'save_debug_images': False,     # save debug images to disk
    'verbose_logging': False,       # enable verbose logging
    'show_timing_info': False,      # show timing information
}

# Hardware compatibility
HARDWARE = {
    'supported_cameras': [          # list of supported camera models
        'QHY5II',
        'QHY367C',
        'QHY294C',
        'QHY183C',
        # Add more camera models as needed
    ],
    'max_image_size': (7400, 4950), # maximum supported image size
    'min_exposure_time': 1000,      # minimum exposure time (microseconds)
    'max_exposure_time': 10000000,  # maximum exposure time (microseconds)
}

# ==================== User Customization ====================

def load_user_config():
    """Load user configuration from file if it exists"""
    import os
    import configparser
    
    config_file = PATHS['config_file']
    if os.path.exists(config_file):
        try:
            config = configparser.ConfigParser()
            config.read(config_file)
            
            # Update settings from config file
            # This is a simplified example - implement full config loading as needed
            if 'CAMERA' in config:
                for key, value in config['CAMERA'].items():
                    if key in DEFAULT_CAMERA_PARAMS:
                        DEFAULT_CAMERA_PARAMS[key] = int(value)
            
            if 'SERIAL' in config:
                for key, value in config['SERIAL'].items():
                    if key in SERIAL_CONFIG:
                        if key in ['baud_rate', 'data_bits', 'stop_bits']:
                            SERIAL_CONFIG[key] = int(value)
                        elif key in ['timeout', 'write_timeout']:
                            SERIAL_CONFIG[key] = float(value)
                        else:
                            SERIAL_CONFIG[key] = value
            
            print("User configuration loaded successfully")
            
        except Exception as e:
            print(f"Warning: Failed to load user configuration: {e}")

def save_user_config():
    """Save current configuration to file"""
    import configparser
    
    config = configparser.ConfigParser()
    
    # Save camera settings
    config['CAMERA'] = {}
    for key, value in DEFAULT_CAMERA_PARAMS.items():
        config['CAMERA'][key] = str(value)
    
    # Save serial settings
    config['SERIAL'] = {}
    for key, value in SERIAL_CONFIG.items():
        config['SERIAL'][key] = str(value)
    
    # Save test settings
    config['TEST'] = {}
    for key, value in TEST_PARAMS.items():
        config['TEST'][key] = str(value)
    
    try:
        with open(PATHS['config_file'], 'w') as f:
            config.write(f)
        print("User configuration saved successfully")
        return True
    except Exception as e:
        print(f"Warning: Failed to save user configuration: {e}")
        return False

# Load user configuration on import
load_user_config()
