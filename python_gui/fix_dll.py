#!/usr/bin/env python3
"""
QHYCCD DLL Fix Script
Automatically fix DLL loading issues for 64-bit systems
"""

import os
import sys
import shutil
import platform

def main():
    """Main fix function"""
    print("QHYCCD DLL Fix Tool")
    print("=" * 30)
    print(f"Python architecture: {platform.architecture()[0]}")
    
    current_dir = os.path.dirname(__file__)
    delphi_dir = os.path.join(current_dir, '..', 'delphi')
    
    # Check if we have 64-bit Python
    if platform.architecture()[0] != '64bit':
        print("❌ This fix is for 64-bit Python only")
        print(f"Your Python is {platform.architecture()[0]}")
        input("Press Enter to exit...")
        return
    
    print("✅ 64-bit Python detected")
    
    # Look for 64-bit DLL in delphi directory
    dll_candidates = [
        os.path.join(delphi_dir, 'qhyccd294-64.dll'),
        os.path.join(delphi_dir, 'qhyccd.dll'),
        os.path.join(current_dir, 'qhyccd.dll')
    ]
    
    print("\nSearching for 64-bit DLL...")
    source_dll = None
    
    for dll_path in dll_candidates:
        if os.path.exists(dll_path):
            print(f"✅ Found: {dll_path}")
            # Check if it's likely 64-bit (rough size check)
            size = os.path.getsize(dll_path)
            print(f"   Size: {size:,} bytes")
            
            if size > 500000:  # 64-bit DLLs are usually larger
                source_dll = dll_path
                print("   ✅ Appears to be 64-bit DLL")
                break
            else:
                print("   ⚠ Might be 32-bit DLL (small size)")
        else:
            print(f"❌ Not found: {dll_path}")
    
    if not source_dll:
        print("\n❌ No suitable 64-bit DLL found!")
        print("\nPlease:")
        print("1. Download QHYCCD SDK from official website")
        print("2. Copy qhyccd.dll (64-bit) to python_gui directory")
        print("3. Or copy qhyccd294-64.dll to delphi directory")
        input("Press Enter to exit...")
        return
    
    # Copy DLL to current directory
    target_dll = os.path.join(current_dir, 'qhyccd.dll')
    
    if source_dll == target_dll:
        print(f"\n✅ DLL already in correct location: {target_dll}")
    else:
        try:
            print(f"\n📋 Copying DLL...")
            print(f"   From: {source_dll}")
            print(f"   To:   {target_dll}")
            
            # Backup existing DLL if it exists
            if os.path.exists(target_dll):
                backup_dll = target_dll + '.backup'
                shutil.copy2(target_dll, backup_dll)
                print(f"   📦 Backed up existing DLL to: {backup_dll}")
            
            # Copy the DLL
            shutil.copy2(source_dll, target_dll)
            print("   ✅ DLL copied successfully!")
            
        except Exception as e:
            print(f"   ❌ Failed to copy DLL: {e}")
            input("Press Enter to exit...")
            return
    
    # Test the DLL
    print("\n🧪 Testing DLL...")
    try:
        import ctypes
        
        # Try WinDLL first (better for 64-bit)
        if sys.platform == 'win32':
            dll = ctypes.WinDLL(target_dll)
        else:
            dll = ctypes.CDLL(target_dll)
        
        print("   ✅ DLL loaded successfully")
        
        # Test basic function
        if hasattr(dll, 'InitQHYCCDResource'):
            print("   ✅ Has required QHYCCD functions")
            
            # Try calling the function
            dll.InitQHYCCDResource.restype = ctypes.c_uint32
            result = dll.InitQHYCCDResource()
            print(f"   ✅ InitQHYCCDResource() returned: {result}")
            
            print("\n🎉 SUCCESS! DLL is working correctly!")
            print("You can now run the application:")
            print("   python simple_run.py")
            
        else:
            print("   ❌ Missing required QHYCCD functions")
            print("   This might not be a valid QHYCCD DLL")
            
    except Exception as e:
        print(f"   ❌ DLL test failed: {e}")
        
        if "不是有效的 Win32 应用程序" in str(e):
            print("   💡 This is still an architecture mismatch")
            print("   Please ensure you have the correct 64-bit DLL")
    
    print("\n" + "=" * 30)
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
