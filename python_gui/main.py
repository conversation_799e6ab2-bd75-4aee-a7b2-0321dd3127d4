"""
QHYCCD Sensor Performance Test Tools - Python GUI Implementation
Main application window and GUI components
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import logging
from typing import Optional, Dict, Any

# Import our modules
from camera_controller import QHYCCDController, ControlID
from serial_controller import Serial<PERSON><PERSON>roller
from image_processor import ImageProcessor
from test_automation import TestAutomation, TestType
from utils import *

class QHYCCDTestApp:
    """Main application class"""
    
    def __init__(self):
        # Initialize logging
        logging.basicConfig(level=logging.INFO, 
                          format='%(asctime)s - %(levelname)s - %(message)s')
        
        # Initialize controllers
        self.camera = QHYCCDController()
        self.serial = SerialController()
        self.image_processor = ImageProcessor()
        self.test_automation = TestAutomation(self.camera, self.serial, self.image_processor)
        
        # GUI state
        self.is_live_preview = False
        self.preview_thread = None
        self.stop_preview = False
        
        # Current values
        self.current_gain = 0
        self.current_exposure = 45000
        self.current_offset = 30
        self.current_gain_r = 100
        self.current_gain_g = 100
        self.current_gain_b = 100
        
        # Register manipulation
        self.bit_manipulator = BitManipulator()
        
        # Create GUI
        self.create_gui()
        
        # Setup test automation callbacks
        self.test_automation.set_callbacks(
            progress_callback=self.on_test_progress,
            result_callback=self.on_test_result,
            image_callback=self.on_test_image
        )
    
    def create_gui(self):
        """Create main GUI window"""
        self.root = tk.Tk()
        self.root.title("QHYCCD Sensor Performance Test Tools")
        self.root.geometry("1453x845")
        self.root.resizable(True, True)
        
        # Create main frames
        self.create_connection_frame()
        self.create_register_frame()
        self.create_parameter_frame()
        self.create_image_frame()
        self.create_test_frame()
        self.create_serial_frame()
        self.create_status_frame()
        
        # Bind close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_connection_frame(self):
        """Create camera connection controls"""
        frame = ttk.LabelFrame(self.root, text="Camera Connection", padding=10)
        frame.place(x=10, y=8, width=460, height=60)
        
        # Read mode combo
        ttk.Label(frame, text="Read Mode:").place(x=0, y=0)
        self.combo_read_mode = ttk.Combobox(frame, width=15, state="readonly")
        self.combo_read_mode.place(x=80, y=0)
        
        # Total modes label
        self.label_total_modes = ttk.Label(frame, text="Total ReadModes: 0")
        self.label_total_modes.place(x=210, y=0)
        
        # Connect button
        self.btn_connect = ttk.Button(frame, text="连接相机", command=self.connect_camera)
        self.btn_connect.place(x=342, y=0, width=100, height=30)
        
        # COM port selection
        ttk.Label(frame, text="COM Port:").place(x=0, y=25)
        self.combo_com_port = ttk.Combobox(frame, width=10, values=self.get_com_ports())
        self.combo_com_port.set("COM3")
        self.combo_com_port.place(x=80, y=25)
        
        # Serial connect button
        self.btn_serial_connect = ttk.Button(frame, text="打开串口连接", command=self.connect_serial)
        self.btn_serial_connect.place(x=200, y=25, width=100, height=25)
    
    def create_register_frame(self):
        """Create register read/write controls"""
        frame = ttk.LabelFrame(self.root, text="Register Operations", padding=10)
        frame.place(x=8, y=72, width=460, height=250)
        
        # Register address input
        ttk.Label(frame, text="Register Address:").place(x=0, y=0)
        self.entry_reg_addr = ttk.Entry(frame, width=15, font=("Tahoma", 12))
        self.entry_reg_addr.insert(0, "0000")
        self.entry_reg_addr.place(x=0, y=20)
        
        # Register value input
        ttk.Label(frame, text="Register Value:").place(x=120, y=0)
        self.entry_reg_value = ttk.Entry(frame, width=15, font=("Tahoma", 12))
        self.entry_reg_value.insert(0, "0012")
        self.entry_reg_value.place(x=120, y=20)
        
        # Write/Read buttons
        ttk.Button(frame, text="WriteREG", command=self.write_register).place(x=240, y=20, width=80, height=30)
        ttk.Button(frame, text="ReadREG", command=self.read_register).place(x=330, y=20, width=80, height=30)
        
        # Bit manipulation
        ttk.Label(frame, text="Bit Manipulation:").place(x=0, y=60)
        self.entry_bit_reg = ttk.Entry(frame, width=15, font=("Tahoma", 12))
        self.entry_bit_reg.insert(0, "0000")
        self.entry_bit_reg.place(x=0, y=80)
        
        # Bit buttons (16 bits)
        self.bit_buttons = []
        for i in range(8):
            btn = ttk.Button(frame, text="0", width=3, 
                           command=lambda idx=i: self.toggle_bit(idx))
            btn.place(x=120 + i*25, y=80, width=20, height=20)
            self.bit_buttons.append(btn)
        
        for i in range(8):
            btn = ttk.Button(frame, text="0", width=3, 
                           command=lambda idx=i+8: self.toggle_bit(idx))
            btn.place(x=120 + i*25, y=105, width=20, height=20)
            self.bit_buttons.append(btn)
        
        ttk.Button(frame, text="WriteREG", command=self.write_bit_register).place(x=340, y=80, width=80, height=25)
        ttk.Button(frame, text="ReadREG", command=self.read_bit_register).place(x=340, y=110, width=80, height=25)
        
        # Scrollbars for parameter adjustment
        ttk.Label(frame, text="Parameter Adjustment:").place(x=0, y=140)
        
        self.scale_param1 = ttk.Scale(frame, from_=0, to=255, orient=tk.HORIZONTAL, 
                                     command=self.on_scale_change)
        self.scale_param1.place(x=120, y=140, width=200, height=25)
        
        self.scale_param2 = ttk.Scale(frame, from_=0, to=65535, orient=tk.HORIZONTAL,
                                     command=self.on_scale2_change)
        self.scale_param2.place(x=120, y=170, width=200, height=25)
        
        # Parameter display
        self.entry_param_display = ttk.Entry(frame, width=15, font=("Tahoma", 10))
        self.entry_param_display.insert(0, "0000")
        self.entry_param_display.place(x=0, y=170)
        
        # Memory test buttons
        ttk.Button(frame, text="内存测试", command=self.memory_test).place(x=340, y=140, width=80, height=30)
        ttk.Button(frame, text="单帧曝光", command=self.single_exposure).place(x=340, y=175, width=80, height=30)
    
    def create_parameter_frame(self):
        """Create camera parameter controls"""
        frame = ttk.LabelFrame(self.root, text="Camera Parameters", padding=10)
        frame.place(x=8, y=330, width=460, height=400)
        
        # Gain control
        ttk.Label(frame, text="增益:").place(x=0, y=0)
        self.scale_gain = ttk.Scale(frame, from_=0, to=511, orient=tk.HORIZONTAL,
                                   command=self.on_gain_change)
        self.scale_gain.place(x=50, y=0, width=150, height=25)
        
        # Digital gain
        ttk.Label(frame, text="数字增益:").place(x=0, y=30)
        self.scale_digital_gain = ttk.Scale(frame, from_=0, to=1023, orient=tk.HORIZONTAL,
                                           command=self.on_digital_gain_change)
        self.scale_digital_gain.place(x=80, y=30, width=150, height=25)
        
        # Row noise control
        ttk.Label(frame, text="行噪声控制:").place(x=0, y=60)
        self.scale_row_noise = ttk.Scale(frame, from_=1, to=16383, orient=tk.HORIZONTAL,
                                        command=self.on_row_noise_change)
        self.scale_row_noise.set(1)
        self.scale_row_noise.place(x=80, y=60, width=150, height=25)
        
        # SVR control
        ttk.Label(frame, text="SVR噪声控制:").place(x=0, y=90)
        self.scale_svr = ttk.Scale(frame, from_=1, to=16383, orient=tk.HORIZONTAL,
                                  command=self.on_svr_change)
        self.scale_svr.set(1)
        self.scale_svr.place(x=100, y=90, width=150, height=25)
        
        # V blank
        ttk.Label(frame, text="V blank:").place(x=0, y=120)
        self.scale_vblank = ttk.Scale(frame, from_=0, to=63, orient=tk.HORIZONTAL,
                                     command=self.on_vblank_change)
        self.scale_vblank.place(x=60, y=120, width=150, height=25)
        
        # Crop controls
        ttk.Label(frame, text="Crop Start line:").place(x=0, y=150)
        self.scale_crop_start = ttk.Scale(frame, from_=1, to=16383, orient=tk.HORIZONTAL,
                                         command=self.on_crop_start_change)
        self.scale_crop_start.set(1)
        self.scale_crop_start.place(x=100, y=150, width=150, height=25)
        
        ttk.Label(frame, text="Crop Width:").place(x=0, y=180)
        self.scale_crop_width = ttk.Scale(frame, from_=0, to=16383, orient=tk.HORIZONTAL,
                                         command=self.on_crop_width_change)
        self.scale_crop_width.place(x=80, y=180, width=150, height=25)
        
        # Offset control
        ttk.Label(frame, text="输出OFFSET:").place(x=0, y=210)
        self.scale_offset = ttk.Scale(frame, from_=1, to=2047, orient=tk.HORIZONTAL,
                                     command=self.on_offset_change)
        self.scale_offset.set(1)
        self.scale_offset.place(x=100, y=210, width=150, height=25)
        
        # RGB gains
        ttk.Label(frame, text="R:").place(x=0, y=240)
        self.scale_gain_r = ttk.Scale(frame, from_=15, to=4095, orient=tk.HORIZONTAL,
                                     command=self.on_gain_r_change)
        self.scale_gain_r.set(15)
        self.scale_gain_r.place(x=20, y=240, width=150, height=25)
        
        ttk.Label(frame, text="G:").place(x=0, y=270)
        self.scale_gain_g = ttk.Scale(frame, from_=15, to=4095, orient=tk.HORIZONTAL,
                                     command=self.on_gain_g_change)
        self.scale_gain_g.set(15)
        self.scale_gain_g.place(x=20, y=270, width=150, height=25)
        
        ttk.Label(frame, text="B:").place(x=0, y=300)
        self.scale_gain_b = ttk.Scale(frame, from_=15, to=4095, orient=tk.HORIZONTAL,
                                     command=self.on_gain_b_change)
        self.scale_gain_b.set(15)
        self.scale_gain_b.place(x=20, y=300, width=150, height=25)
        
        # Additional controls
        ttk.Label(frame, text="black dummy clamp start line:").place(x=0, y=330)
        self.scale_clamp_start = ttk.Scale(frame, from_=0, to=1023, orient=tk.HORIZONTAL,
                                          command=self.on_clamp_start_change)
        self.scale_clamp_start.place(x=200, y=330, width=150, height=25)
        
        ttk.Label(frame, text="black dummy clamp number:").place(x=0, y=360)
        self.scale_clamp_number = ttk.Scale(frame, from_=0, to=2047, orient=tk.HORIZONTAL,
                                           command=self.on_clamp_number_change)
        self.scale_clamp_number.place(x=200, y=360, width=150, height=25)
    
    def create_image_frame(self):
        """Create image display area"""
        frame = ttk.LabelFrame(self.root, text="Image Display", padding=10)
        frame.place(x=478, y=8, width=720, height=420)
        
        # Preview image
        self.canvas_preview = tk.Canvas(frame, width=352, height=201, bg='black')
        self.canvas_preview.place(x=0, y=0)
        ttk.Label(frame, text="Preview").place(x=0, y=205)
        
        # Flat images
        self.canvas_flat1 = tk.Canvas(frame, width=352, height=201, bg='black')
        self.canvas_flat1.place(x=360, y=0)
        ttk.Label(frame, text="Flat A").place(x=360, y=205)
        
        self.canvas_flat2 = tk.Canvas(frame, width=352, height=201, bg='black')
        self.canvas_flat2.place(x=0, y=220)
        ttk.Label(frame, text="Flat B").place(x=0, y=425)
        
        # Bias images
        self.canvas_bias1 = tk.Canvas(frame, width=352, height=201, bg='black')
        self.canvas_bias1.place(x=360, y=220)
        ttk.Label(frame, text="Bias A").place(x=360, y=425)
        
        # Control buttons
        ttk.Button(frame, text="SHOWIMG", command=self.start_live_preview).place(x=10, y=450, width=80, height=30)
        ttk.Button(frame, text="STOPSHOW", command=self.stop_live_preview).place(x=100, y=450, width=80, height=30)
        ttk.Button(frame, text="高增益", command=self.high_gain_mode).place(x=190, y=450, width=80, height=30)
        ttk.Button(frame, text="输出噪声", command=self.output_noise).place(x=280, y=450, width=80, height=30)
    
    def create_test_frame(self):
        """Create test automation controls"""
        frame = ttk.LabelFrame(self.root, text="Test Automation", padding=10)
        frame.place(x=1200, y=8, width=240, height=500)
        
        # Live frame button
        ttk.Button(frame, text="LiveFrame", command=self.start_live_frame).place(x=0, y=0, width=100, height=40)
        
        # Exposure time adjustment
        ttk.Button(frame, text="ExpTimeAdj", command=self.start_exposure_adjustment).place(x=0, y=50, width=100, height=40)
        
        # Run test button
        ttk.Button(frame, text="Run Test", command=self.start_test).place(x=0, y=100, width=100, height=40)
        
        # Stop test button
        ttk.Button(frame, text="Stop Test", command=self.stop_test).place(x=110, y=100, width=100, height=40)
        
        # Gain control
        ttk.Button(frame, text="Gain", command=self.gain_control).place(x=0, y=150, width=100, height=25)
        
        # Gain percentage buttons
        gain_percentages = ["0%", "25%", "50%", "75%", "100%"]
        for i, pct in enumerate(gain_percentages):
            ttk.Button(frame, text=pct, width=8,
                      command=lambda p=pct: self.set_gain_percentage(p)).place(x=i*40, y=180, width=35, height=25)
        
        # Exposure time buttons (ms)
        exp_times = ["1", "2", "5", "7", "10", "15", "20", "25", "30", "40", "50", "100", "200", "500"]
        for i, exp in enumerate(exp_times):
            row = i // 5
            col = i % 5
            ttk.Button(frame, text=exp, width=6,
                      command=lambda e=exp: self.set_exposure_time(int(e))).place(
                          x=col*40, y=210 + row*30, width=35, height=25)
        
        # Gain start/step controls
        ttk.Label(frame, text="开始增益:").place(x=0, y=320)
        self.entry_gain_start = ttk.Entry(frame, width=8)
        self.entry_gain_start.insert(0, "0")
        self.entry_gain_start.place(x=60, y=320)
        
        ttk.Label(frame, text="增益间隔:").place(x=130, y=320)
        self.entry_gain_step = ttk.Entry(frame, width=8)
        self.entry_gain_step.insert(0, "5")
        self.entry_gain_step.place(x=180, y=320)
        
        # Gain sliders
        ttk.Label(frame, text="gain:").place(x=0, y=350)
        self.scale_main_gain = ttk.Scale(frame, from_=0, to=255, orient=tk.HORIZONTAL,
                                        command=self.on_main_gain_change)
        self.scale_main_gain.place(x=40, y=350, width=180, height=20)
        
        ttk.Label(frame, text="gainR:").place(x=0, y=375)
        self.scale_test_gain_r = ttk.Scale(frame, from_=0, to=255, orient=tk.HORIZONTAL,
                                          command=self.on_test_gain_r_change)
        self.scale_test_gain_r.set(128)
        self.scale_test_gain_r.place(x=40, y=375, width=180, height=20)
        
        ttk.Label(frame, text="gainG:").place(x=0, y=400)
        self.scale_test_gain_g = ttk.Scale(frame, from_=0, to=255, orient=tk.HORIZONTAL,
                                          command=self.on_test_gain_g_change)
        self.scale_test_gain_g.set(128)
        self.scale_test_gain_g.place(x=40, y=400, width=180, height=20)
        
        ttk.Label(frame, text="gainB:").place(x=0, y=425)
        self.scale_test_gain_b = ttk.Scale(frame, from_=0, to=255, orient=tk.HORIZONTAL,
                                          command=self.on_test_gain_b_change)
        self.scale_test_gain_b.set(128)
        self.scale_test_gain_b.place(x=40, y=425, width=180, height=20)
    
    def create_serial_frame(self):
        """Create serial port controls"""
        frame = ttk.LabelFrame(self.root, text="Serial Control", padding=10)
        frame.place(x=1200, y=520, width=240, height=100)
        
        # Serial port buttons
        ttk.Button(frame, text="Comport Open", command=self.open_comport).place(x=0, y=0, width=75, height=25)
        ttk.Button(frame, text="OFF", command=self.flat_panel_off).place(x=80, y=0, width=75, height=25)
        ttk.Button(frame, text="ON", command=self.flat_panel_on).place(x=160, y=0, width=75, height=25)
        
        ttk.Button(frame, text="Clear Memo", command=self.clear_memo).place(x=0, y=30, width=75, height=25)
    
    def create_status_frame(self):
        """Create status and memo area"""
        frame = ttk.LabelFrame(self.root, text="Status & Log", padding=10)
        frame.place(x=1200, y=630, width=240, height=200)
        
        # Memo/log area
        self.text_memo = tk.Text(frame, width=28, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=self.text_memo.yview)
        self.text_memo.configure(yscrollcommand=scrollbar.set)
        
        self.text_memo.place(x=0, y=0, width=200, height=160)
        scrollbar.place(x=205, y=0, width=20, height=160)
        
        # Add initial message
        self.add_memo("QHYCCD Test Application Started")
    
    # Event handlers and methods will be added in the next part...
    
    def run(self):
        """Start the GUI application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = QHYCCDTestApp()
    app.run()
