// ---------------------------------------------------------------------------

#include <vcl.h>
#pragma hdrstop

#include <stdio.h>

#include <tchar.h>
#include "cv.h"
#include "highgui.h"

#include "Unit1.h"
#include "math.h"

#include "qhyccd.h"
#include "qhyccderr.h"
#include <time.h>
// ---------------------------------------------------------------------------
#pragma package(smart_init)

#pragma link "ComPort"
#pragma resource "*.dfm"




#define debug_print RichEdit1->Text  = RichEdit1->Text   + "\n" +
#define debug_print_cls RichEdit1->Text="";

HANDLE g_hCam;
bool camlink = false;
unsigned char headernum = 0;
unsigned int total_length2nd = 0;

int g_cacheSize;
unsigned char** g_frameCache;
int g_readFrame;
int g_displayFrame;
int g_mapSize;
BOOL* g_vMap;
int g_maxLevel;
BOOL g_adjLevel;
BOOL g_stop;
int startCounter = 0;

int X = 1024;
int Y = 768;
unsigned long ImagePackageSize = 1024 * 768;

TForm1 *Form1;
bool ex;
unsigned int gain=0;   //current global gain ֮ǰΪchar���ͣ����ǲ�������QHY367C�ķ�Χ��0-2064�����ָ�Ϊint��_YRX

int chipID=0x02;

unsigned char stop = 0,flag_onCapture = 0;
unsigned int global_counter = 0;

unsigned long CurrentExpTime=45000;
double CurrentFlatRMS=0;


int step=0; 					//���Բ������_YRX
bool isTest=0;                	//Test���б��_YRX
bool isFindExposure=0;          //�ع�ʱ��������_YRX
bool isGainChanged = 0;         //����������_YRX
bool isColorGainChanged=0;		//��ɫ����������_YRX
unsigned char gainR=100;
unsigned char gainG=100;
unsigned char gainB=100;
unsigned int  expUnit=1000;   //exposure time unit (1,1000,1000000 = us  ms  sec)
unsigned char imageHead[64];

bool isGainBarVisible=0;



int gainStart=0;
int gainStep=10;

unsigned char MSB(unsigned int i){
unsigned int j;
j=(i&~0x00ff)>>8;
return j;

}

unsigned char LSB(unsigned int i){
unsigned int j;
j=i&~0xff00;
return j;

}

//*****************************************************************************************************************************************

Graphics::TBitmap *bitmapHist = new Graphics::TBitmap();

void __fastcall showHistOnWindows16(IplImage *img,TImage *timg) {
	// ���κδ�С��ͼ����ʾ��Image2��
	// ����ͼ��Ϊ��ɫͼ��8λ
	unsigned char pixel;
	int x, y;

	BYTE *newscanline;

	IplImage *tempImgMono,*tempImgMono16,*tempImgColor,*tempImgColor16;

	tempImgMono   = cvCreateImage(cvSize(timg->Width, timg->Height), IPL_DEPTH_8U,  1);
	tempImgMono16 = cvCreateImage(cvSize(timg->Width, timg->Height), IPL_DEPTH_16U, 1);

	tempImgColor   = cvCreateImage(cvSize(timg->Width, timg->Height), IPL_DEPTH_8U,  3);
	tempImgColor16 = cvCreateImage(cvSize(timg->Width, timg->Height), IPL_DEPTH_16U, 3);


	x = timg->Width;
	y = timg->Height;


	//resize the input image to a small image

	if(img->nChannels==1){

	   if(img->depth==16) cvResize(img,tempImgMono16,1);
	   else               cvResize(img,tempImgMono  ,1);

	}

	else{
	   if(img->depth==16) cvResize(img,tempImgColor16,1);
	   else               cvResize(img,tempImgColor  ,1);

    }



	//convert all mono image to color image
	if(img->nChannels ==1 && img->depth==16) cvCvtColor(tempImgMono16,tempImgColor16,CV_GRAY2BGR);
	if(img->nChannels ==1 && img->depth==8)  cvCvtColor(tempImgMono  ,tempImgColor  ,CV_GRAY2BGR);


	//convert 16bit image to 8bit imag
	long s=0;
	long k=0;
	if(img->depth==16){

		for(int j=0;j<y;j++){
		  for(int i=0;i<x;i++){

		  tempImgColor->imageData[k]=tempImgColor16->imageData[s+1];
		  k=k+1;
		  s=s+2;
		  tempImgColor->imageData[k]=tempImgColor16->imageData[s+1];
		  k=k+1;
		  s=s+2;
		  tempImgColor->imageData[k]=tempImgColor16->imageData[s+1];
		  k=k+1;
		  s=s+2;

		  }
		}

	}




	for (int i = 0; i < y; i++) {
		newscanline = static_cast<BYTE*>(bitmapHist->ScanLine[i]);
		CopyMemory(newscanline, tempImgColor->imageData + i*x*3, x*3);
	}

	cvReleaseImage(&tempImgMono);
	cvReleaseImage(&tempImgMono16);
	cvReleaseImage(&tempImgColor);
	cvReleaseImage(&tempImgColor16);

	timg->Picture->Graphic = bitmapHist;
	Wrong:
}





//*****************************************************************************************************************************************

// ---------------------------------------------------------------------------
__fastcall TForm1::TForm1(TComponent* Owner) : TForm(Owner) {
}


unsigned int hextoi(char *hexstring) {
	register char *h;
	register unsigned int c, v;

	v = 0;
	h = hexstring;
	if (*h == '0' && (*(h + 1) == 'x' || *(h + 1) == 'X')) {
		h += 2;
	}
	while ((c = (unsigned int) * h++) != 0) {
		if (c >= '0' && c <= '9') {
			c -= '0';
		}
		else if (c >= 'a' && c <= 'f') {
			c = (c - 'a') + 10;
		}
		else if (c >= 'A' && c <= 'F') {
			c = (c - 'A') + 10;
		}
		else {
			break;
		}
		v = (v * 0x10) + c;
	}
	return v;
}

// ---------------------------------------------------------------------------

void __fastcall TForm1::Button1Click(TObject *Sender)
{
	 char camid[64];
	 int n;
	 AnsiString CameraName,ColorType, ProduceDate, FactoryLocation;

	 unsigned int  TotalReadModes;

	 if(g_hCam == NULL)
	 {
		 InitQHYCCDResource();
		 int num = ScanQHYCCD();
		 if(num > 0)
		 {
			 GetQHYCCDId(0,camid);
			 g_hCam = OpenQHYCCD(camid);
			 camlink = true;
			 Button1->Caption = AnsiString(camid);;


			 GetQHYCCDNumberOfReadModes(g_hCam, &TotalReadModes);
			 LabelTotalReadMode->Caption = "Total ReadModes:"+AnsiString(TotalReadModes);



			 SetQHYCCDReadMode(g_hCam,  ComboBoxReadMode->ItemIndex);

			 char readModeName[64];
			 GetQHYCCDReadModeName(g_hCam,ComboBoxReadMode->ItemIndex,readModeName);

			 Memo1->Text = Memo1->Text +  "QHYCCD Read Mode #"+ AnsiString(ComboBoxReadMode->ItemIndex)+ "\r\n"+ readModeName +"\r\n";


			 //
			 //LabelTotalReadMode->Caption = AnsiString(readModeName);

		 }

	 }
}
// ---------------------------------------------------------------------------

void writeIMX183(unsigned char index,unsigned char value){

	unsigned char data[2];
	unsigned int length = 2;


	data[0]=value &~ 0xff00; //IMX174�ļĴ�������Ϊ8λ������ȥ����8λ

	QHYCCDVendRequestWrite(g_hCam,0xb8,0x81,index,1,data);


}

void writeMN34230(unsigned short index, unsigned short value){


	unsigned char data[2];
	unsigned int length = 2;



	   data[0] = value &~ 0xff00;
	   data[1] = (value &~ 0x00ff)>>8;


	  QHYCCDVendRequestWrite(g_hCam,0xb8,0x81,index,length,data);

}


void writeIMX094(unsigned char index,unsigned char value){



	unsigned char data[2];
	unsigned int length = 1;



	   data[0] = value;



	  QHYCCDVendRequestWrite(g_hCam,0xb8,0x02,index,length,data);

}

void writeFPGA(unsigned char index,unsigned char value){

	unsigned char data[2];


	data[0]=0x00; //IMX174�ļĴ�������Ϊ8λ������ȥ����8λ



	QHYCCDVendRequestWrite(g_hCam,0xb9,value,index,1,data);


}

void IMX094_ROI(unsigned short START_LINE,unsigned short out_line){

	unsigned char data[5];



	data[0] = (START_LINE &~ 0x00ff) >>8;
	data[1] = START_LINE &~ 0xff00;

	data[2] = (out_line &~ 0x00ff) >>8;
	data[3] = out_line &~ 0xff00;

	//45/46 need set to data[2] and data[3]
	writeFPGA(45, data[2]);//max_line_valid
	writeFPGA(46, data[3]);
	writeFPGA(47, 0x03);//max_pix_valid
	writeFPGA(48, 0x9d);

	writeIMX094(0x01,01);//=01 OR =02

	writeIMX094(0x09,data[0]);//START OF LINE --- MSB
	writeIMX094(0x08,data[1]);//START OF LINE --- LSB

	writeIMX094(0x0b,data[2]);//READ OUT LINE --- MSB
	writeIMX094(0x0a,data[3]);//READ OUT LINE --- LSB


}


void IMX094_FULL(){
 writeIMX094(0x00, 0x04 );
 writeIMX094(0x01, 0x00 );
 writeIMX094(0x1E, 0x48 );
 writeIMX094(0x45, 0x07 );

 writeIMX094(0x46, 0x62 );
 writeIMX094(0x47, 0x00 );
 writeIMX094(0x48, 0x6F );
 writeIMX094(0x49, 0x60 );
 writeIMX094(0x4a, 0x55 );
 writeIMX094(0x4b, 0xD7 );
 writeIMX094(0x4c, 0x44 );
 writeIMX094(0x4d, 0x01 );
 writeIMX094(0x4e, 0x13 );
 writeIMX094(0x4f, 0x05 );
 writeIMX094(0x50, 0x73 );
 writeIMX094(0x51, 0x0A );
 writeIMX094(0x52, 0x0F );
 writeIMX094(0x53, 0x30 );
 writeIMX094(0x54, 0x1C );
 writeIMX094(0x55, 0x26 );
 writeIMX094(0x56, 0x00 );
 writeIMX094(0x57, 0x03 );
 writeIMX094(0x58, 0x30 );

 writeIMX094(0x64, 0xAE  );
 writeIMX094(0x65, 0x00 );
 writeIMX094(0x66, 0x00 );
 writeIMX094(0x67, 0x00 );
 writeIMX094(0x68, 0x00 );
 writeIMX094(0x69, 0x08 );
 writeIMX094(0x6a, 0x00 );
 writeIMX094(0x6b, 0x04 );
 writeIMX094(0x6c, 0x31 );
 writeIMX094(0x6d, 0x83 );
 writeIMX094(0x6e, 0xE0 );
 writeIMX094(0x6f, 0xCB );
 writeIMX094(0x70, 0xB0 );
 writeIMX094(0x71, 0x31 );
 writeIMX094(0x72, 0x26 );
 writeIMX094(0x73, 0x0A );
 writeIMX094(0x74, 0xF6 );
 writeIMX094(0x75, 0xFF );
 writeIMX094(0x76, 0x4C );
 writeIMX094(0x77, 0x28 );
 writeIMX094(0x78, 0x04 );
 writeIMX094(0x79, 0x4B );
 writeIMX094(0x7a, 0x00 );
 writeIMX094(0x7b, 0xC0 );
 writeIMX094(0x7c, 0x01 );

 writeIMX094(0x9f, 0x40 );
 writeIMX094(0xa0, 0x45 );
 writeIMX094(0xa1, 0x1C );
 writeIMX094(0xa2, 0x02 );
 writeIMX094(0xa3, 0x00 );
 writeIMX094(0xa4, 0x86 );
 writeIMX094(0xa5, 0x99 );
 writeIMX094(0xa6, 0xA6 );
 writeIMX094(0xa7, 0x58 );
 writeIMX094(0xa8, 0xC2 );
 writeIMX094(0xa9, 0x92 );
 writeIMX094(0xaa, 0x28 );
 writeIMX094(0xab, 0x80 );
 writeIMX094(0xac, 0xCA );
 writeIMX094(0xad, 0x01 );
 writeIMX094(0xae, 0x26 );
 writeIMX094(0xaf, 0x59 );
 writeIMX094(0xb0, 0x80 );
 writeIMX094(0xb1, 0xCA );

 writeIMX094(0xf8, 0x10 );
}

void IMX094_FULL_v_1_3(){
 writeIMX094(0x00, 0x34 );
 writeIMX094(0x01, 0x00 );
 writeIMX094(0x0c, 0x04 );
 writeIMX094(0x11, 0x04 );
 writeIMX094(0x1c, 0x02 );
 writeIMX094(0x1f, 0x04 );

 writeIMX094(0x45, 0x07 );
 writeIMX094(0x46, 0x62 );
 writeIMX094(0x47, 0x00 );
 writeIMX094(0x48, 0x6F );
 writeIMX094(0x49, 0x60 );
 writeIMX094(0x4a, 0x55 );
 writeIMX094(0x4b, 0xD7 );
 writeIMX094(0x4c, 0x44 );
 writeIMX094(0x4d, 0x01 );
 writeIMX094(0x4e, 0x13 );
 writeIMX094(0x4f, 0x05 );
 writeIMX094(0x50, 0x73 );
 writeIMX094(0x51, 0x0A );
 writeIMX094(0x52, 0x0F );
 writeIMX094(0x53, 0x30 );
 writeIMX094(0x54, 0x1C );
 writeIMX094(0x55, 0x26 );
 writeIMX094(0x56, 0x00 );
 writeIMX094(0x57, 0x03 );
 writeIMX094(0x58, 0x30 );

 writeIMX094(0x6b, 0x04 );
 writeIMX094(0x6c, 0x31 );
 writeIMX094(0x6d, 0x83 );
 writeIMX094(0x6e, 0xE0 );
 writeIMX094(0x6f, 0xCB );
 writeIMX094(0x70, 0xB0 );
 writeIMX094(0x71, 0x31 );
 writeIMX094(0x72, 0x26 );
 writeIMX094(0x73, 0x0A );
 writeIMX094(0x74, 0xF6 );
 writeIMX094(0x75, 0x7e );
 writeIMX094(0x76, 0x4C );
 writeIMX094(0x77, 0x28 );
 writeIMX094(0x78, 0x04 );
 writeIMX094(0x79, 0x4B );
 writeIMX094(0x7a, 0x00 );
 writeIMX094(0x7b, 0xC0 );
 writeIMX094(0x7c, 0x01 );

 writeIMX094(0x9f, 0x70 );
 writeIMX094(0xa0, 0x45 );
 writeIMX094(0xa1, 0x1C );
 writeIMX094(0xa2, 0x02 );
 writeIMX094(0xa3, 0x00 );
 writeIMX094(0xa4, 0x86 );
 writeIMX094(0xa5, 0x99 );
 writeIMX094(0xa6, 0xA6 );
 writeIMX094(0xa7, 0x58 );
 writeIMX094(0xa8, 0xC2 );
 writeIMX094(0xa9, 0x92 );
 writeIMX094(0xaa, 0x28 );
 writeIMX094(0xab, 0x80 );
 writeIMX094(0xac, 0xCA );
 writeIMX094(0xad, 0x01 );
 writeIMX094(0xae, 0x26 );
 writeIMX094(0xaf, 0x59 );
 writeIMX094(0xb0, 0x80 );
 writeIMX094(0xb1, 0xCA );

 writeIMX094(0xf8, 0x10 );
}

void IMX094_10bit_h1_3_v1_3(){
 writeIMX094(0x00, 0x64 );
 writeIMX094(0x01, 0x00 );
 writeIMX094(0x0c, 0x04 );
 writeIMX094(0x11, 0x04 );
 writeIMX094(0x1a, 0x12 );
 writeIMX094(0x1b, 0x02 );
 writeIMX094(0x1c, 0x02 );
 writeIMX094(0x1d, 0x00 );
 writeIMX094(0x1e, 0x02 );
 writeIMX094(0x1f, 0x04 );

 writeIMX094(0x45, 0x07 );
 writeIMX094(0x46, 0x62 );
 writeIMX094(0x47, 0x00 );
 writeIMX094(0x48, 0x6F );
 writeIMX094(0x49, 0x34 );
 writeIMX094(0x4a, 0x38 );
 writeIMX094(0x4b, 0xD7 );
 writeIMX094(0x4c, 0x44 );
 writeIMX094(0x4d, 0x01 );
 writeIMX094(0x4e, 0x13 );
 writeIMX094(0x4f, 0x05 );
 writeIMX094(0x50, 0x73 );
 writeIMX094(0x51, 0x0A );
 writeIMX094(0x52, 0x0F );
 writeIMX094(0x53, 0x30 );
 writeIMX094(0x54, 0x1C );
 writeIMX094(0x55, 0x26 );
 writeIMX094(0x56, 0x00 );
 writeIMX094(0x57, 0x03 );
 writeIMX094(0x58, 0x30 );

 writeIMX094(0x61, 0x90 );
 writeIMX094(0x62, 0x05 );
 writeIMX094(0x63, 0x00 );
 writeIMX094(0x64, 0x52 );
 writeIMX094(0x65, 0x00 );
 writeIMX094(0x66, 0x00 );
 writeIMX094(0x67, 0x00 );
 writeIMX094(0x68, 0x00 );
 writeIMX094(0x69, 0x0a );
 writeIMX094(0x6a, 0x00 );

 writeIMX094(0x6b, 0x04 );
 writeIMX094(0x6c, 0x31 );
 writeIMX094(0x6d, 0x83 );
 writeIMX094(0x6e, 0xc0 );
 writeIMX094(0x6f, 0x47 );
 writeIMX094(0x70, 0x1f );
 writeIMX094(0x71, 0x20 );
 writeIMX094(0x72, 0x0a );
 writeIMX094(0x73, 0x0A );
 writeIMX094(0x74, 0xa6 );
 writeIMX094(0x75, 0x56 );
 writeIMX094(0x76, 0x14 );


 writeIMX094(0x7b, 0x00 );
 writeIMX094(0x7c, 0x02 );
 writeIMX094(0x7d, 0x02 );
 writeIMX094(0x7e, 0x28 );
 writeIMX094(0x7f, 0x01 );
 writeIMX094(0x80, 0x03 );
 writeIMX094(0x81, 0x24 );
 writeIMX094(0x82, 0x21 );
 writeIMX094(0x83, 0x5f );

 writeIMX094(0x9f, 0x70 );
 writeIMX094(0xa0, 0x4a );
 writeIMX094(0xa1, 0x1C );
 writeIMX094(0xa2, 0x02 );
 writeIMX094(0xa3, 0x00 );
 writeIMX094(0xa4, 0x86 );
 writeIMX094(0xa5, 0xb1 );
 writeIMX094(0xa6, 0x69 );
 writeIMX094(0xa7, 0x90 );
 writeIMX094(0xa8, 0x79 );
 writeIMX094(0xa9, 0x92 );
 writeIMX094(0xaa, 0x28 );
 writeIMX094(0xab, 0x44 );
 writeIMX094(0xac, 0x90 );
 writeIMX094(0xad, 0x01 );
 writeIMX094(0xae, 0x17 );
 writeIMX094(0xaf, 0x3c );
 writeIMX094(0xb0, 0x44 );
 writeIMX094(0xb1, 0x90 );

 writeIMX094(0xf8, 0x10 );
}

void start_14bit_full(){


	writeFPGA(36,0);

	writeFPGA(0x0b,2);
	writeFPGA(1,0);
	writeFPGA(30,1);

	writeFPGA(0,0);//cmos_reset
	writeFPGA(0,1);
	writeFPGA(2,1);//

	writeFPGA(22,0);//VMAX
	writeFPGA(23,0);
	writeFPGA(24,0x13);
	writeFPGA(25,0x7a);

	writeFPGA(26,0);//HMAX
	writeFPGA(27,0);
	writeFPGA(28,0x08);
	writeFPGA(29,0xe4);

	writeFPGA(45, 0x13);//max_line_valid
	writeFPGA(46, 0x5c);
	writeFPGA(47, 0x03);//max_pix_valid
	writeFPGA(48, 0x9d);

	IMX094_FULL();

}

void start_10bit_33bin(){

	writeFPGA(36,0);

	writeFPGA(0x0b,2);
	writeFPGA(1,0);
	writeFPGA(30,1);

	writeFPGA(0,0);//cmos_reset
	writeFPGA(0,1);
	writeFPGA(2,0);//

	writeFPGA(22,0);//VMAX
	writeFPGA(23,0);
	writeFPGA(24,0x06);
	writeFPGA(25,0x82);

	writeFPGA(26,0);//HMAX
	writeFPGA(27,0);
	writeFPGA(28,0x02);
	writeFPGA(29,0x80);

	  /*
	//UpdateTrackBar and Label
	TrackBar18->Position = 1666;
	Label13->Caption = "VMAX1:"+AnsiString(TrackBar18->Position );
	TrackBar19->Position = 2276;
	Label14->Caption = "HMAX1:"+AnsiString(TrackBar19->Position ) ;
		*/

	writeFPGA(45, 0x06);//max_line_valid
	writeFPGA(46, 0x74);
	writeFPGA(47, 0x01);//max_pix_valid
	writeFPGA(48, 0x35);

	IMX094_10bit_h1_3_v1_3();
 }

void setOFFSET(int i){

 writeIMX094(0x16, LSB(i) );

 writeIMX094(0x17, (MSB(i))&~0xf8 );

}



//clear DDR
void clearDDR_pulse(void){
   writeFPGA(1,1);       //��λDDR
   Sleep(10);
   writeFPGA(1,0);
}
void clearDDR_clearing_on(void){
		writeFPGA(1,1);       //��λDDR
}

void clearDDR_clearing_off(void){
	writeFPGA(1,0) ;
}


void OB_OFF(void){

}

void OB_ON(void){

}


void setFreqDiv(unsigned char i){
	writeFPGA(11,i );
}

void EnableDDR(void){

		writeFPGA(30,1);

}

void DisableDDR(void){

		writeFPGA(30,0);

}

void DDR_Threshold(int i){
writeFPGA(31, 0);
writeFPGA(32, MSB(i) );
writeFPGA(33, LSB(i));

}

void setHMAX(unsigned int i){

	  writeFPGA(28,MSB(i));
	  writeFPGA(29,LSB(i));
}

void setVMAX(unsigned int i){

 writeFPGA(23,((i)&~0x00ffff)>>16);
 writeFPGA(24,MSB(i));
 writeFPGA(25,LSB(i));
}


void setIDLE(void){
	 writeFPGA(35,0);
}

void releaseIDLE(void){
    writeFPGA(35,1);
}


void EnableLock(void){
	 writeFPGA(36,1);
}

void DisableLock(void){
	 writeFPGA(36,0);
}

void setLockFrames(unsigned short i){

writeFPGA(37,MSB(i));
writeFPGA(38,LSB(i));
}


void setSHS(int i){

writeIMX094(0x07,MSB(i ));
writeIMX094(0x06,LSB(i ));

}


void setPatchPosition(unsigned long i){

writeFPGA(41,(i & 0xFF000000) >> 24);
writeFPGA(42,(i & 0xFF0000) >> 16);
writeFPGA(43,(i & 0xFF00) >> 8);
writeFPGA(44,(i & 0xFF));

}


void setCropX(int i){

writeFPGA(47,MSB(i));
writeFPGA(48,LSB(i));

}

void setCropY(int i){

writeFPGA(45,MSB(i));
writeFPGA(46,LSB(i));
}




void setIMX094_ReadoutLines(int i){


writeIMX094(0X0B,MSB(i));
writeIMX094(0X0A,LSB(i));

}

void setIMX094_StartLines(int i){


writeIMX094(0X09,MSB(i));
writeIMX094(0X08,LSB(i));

}


void __fastcall TForm1::Button3Click(TObject *Sender) {
	unsigned short index, value;

	index = hextoi(AnsiString(Edit1->Text).c_str());
	value = hextoi(AnsiString(Edit2->Text).c_str());




	writeIMX094(index,value);



}
// ---------------------------------------------------------------------------

void __fastcall TForm1::Button20Click(TObject *Sender) {

	unsigned short index, value;


	index = hextoi(AnsiString(Edit3->Text).c_str());

	value = Button19->Caption.ToInt() * 1 + Button18->Caption.ToInt() * 2 +
		Button17->Caption.ToInt() * 4 + Button16->Caption.ToInt() * 8 +
		Button15->Caption.ToInt() * 16 + Button14->Caption.ToInt() * 32 +
		Button13->Caption.ToInt() * 64 + Button12->Caption.ToInt() * 128 +
		Button11->Caption.ToInt() * 256 + Button10->Caption.ToInt() * 512 +
		Button9->Caption.ToInt() * 1024 + Button8->Caption.ToInt() * 2048 +
		Button7->Caption.ToInt() * 4096 + Button6->Caption.ToInt() * 8192 +
		Button5->Caption.ToInt() * 8192 * 2 + Button4->Caption.ToInt()
		* 8192 * 4;


	writeIMX094(index,value);



}
// ---------------------------------------------------------------------------

void __fastcall TForm1::Button19Click(TObject *Sender) {
	if (Button19->Caption == "1")
		Button19->Caption = "0";
	else
		Button19->Caption = "1";

	Button20->Click();

}
// ---------------------------------------------------------------------------

void __fastcall TForm1::Button18Click(TObject *Sender) {
	if (Button18->Caption == "1")
		Button18->Caption = "0";
	else
		Button18->Caption = "1";

	Button20->Click();

}
// ---------------------------------------------------------------------------

void __fastcall TForm1::Button17Click(TObject *Sender) {
	if (Button17->Caption == "1")
		Button17->Caption = "0";
	else
		Button17->Caption = "1";

	Button20->Click();
}
// ---------------------------------------------------------------------------

void __fastcall TForm1::Button16Click(TObject *Sender) {
	if (Button16->Caption == "1")
		Button16->Caption = "0";
	else
		Button16->Caption = "1";
	Button20->Click();

}
// ---------------------------------------------------------------------------

void __fastcall TForm1::Button15Click(TObject *Sender) {
	if (Button15->Caption == "1")
		Button15->Caption = "0";
	else
		Button15->Caption = "1";
	Button20->Click();

}
// ---------------------------------------------------------------------------

void __fastcall TForm1::Button14Click(TObject *Sender) {
	if (Button14->Caption == "1")
		Button14->Caption = "0";
	else
		Button14->Caption = "1";

	Button20->Click();
}
// ---------------------------------------------------------------------------

void __fastcall TForm1::Button13Click(TObject *Sender) {
	if (Button13->Caption == "1")
		Button13->Caption = "0";
	else
		Button13->Caption = "1";

	Button20->Click();
}
// ---------------------------------------------------------------------------

void __fastcall TForm1::Button12Click(TObject *Sender) {
	if (Button12->Caption == "1")
		Button12->Caption = "0";
	else
		Button12->Caption = "1";

	Button20->Click();
}
// ---------------------------------------------------------------------------

void __fastcall TForm1::Button11Click(TObject *Sender) {
	if (Button11->Caption == "1")
		Button11->Caption = "0";
	else
		Button11->Caption = "1";
	Button20->Click();
}
// ---------------------------------------------------------------------------

void __fastcall TForm1::Button10Click(TObject *Sender) {
	if (Button10->Caption == "1")
		Button10->Caption = "0";
	else
		Button10->Caption = "1";

	Button20->Click();
}
// ---------------------------------------------------------------------------

void __fastcall TForm1::Button9Click(TObject *Sender) {
	if (Button9->Caption == "1")
		Button9->Caption = "0";
	else
		Button9->Caption = "1";
	Button20->Click();

}
// ---------------------------------------------------------------------------

void __fastcall TForm1::Button8Click(TObject *Sender) {
	if (Button8->Caption == "1")
		Button8->Caption = "0";
	else
		Button8->Caption = "1";
	Button20->Click();

}
// ---------------------------------------------------------------------------

void __fastcall TForm1::Button7Click(TObject *Sender) {
	if (Button7->Caption == "1")
		Button7->Caption = "0";
	else
		Button7->Caption = "1";

	Button20->Click();
}
// ---------------------------------------------------------------------------

void __fastcall TForm1::Button6Click(TObject *Sender) {
	if (Button6->Caption == "1")
		Button6->Caption = "0";
	else
		Button6->Caption = "1";

	Button20->Click();
}
// ---------------------------------------------------------------------------

void __fastcall TForm1::Button5Click(TObject *Sender) {
	if (Button5->Caption == "1")
		Button5->Caption = "0";
	else
		Button5->Caption = "1";

	Button20->Click();
}
// ---------------------------------------------------------------------------

void __fastcall TForm1::Button4Click(TObject *Sender) {
	if (Button4->Caption == "1")
		Button4->Caption = "0";
	else
		Button4->Caption = "1";

	Button20->Click();
}
// ---------------------------------------------------------------------------

void __fastcall TForm1::Button21Click(TObject *Sender) {
	unsigned short index, value;
	unsigned char data[2];
	unsigned int length = 2;

	index = hextoi(AnsiString(Edit4->Text).c_str());
	value = ScrollBar1->Position + 256* ScrollBar2->Position ;

	writeIMX094(index,value);
}
// ---------------------------------------------------------------------------

void __fastcall TForm1::Button26Click(TObject *Sender) {
	unsigned short index, value;


	index = hextoi(AnsiString(Edit5->Text).c_str());
	value = ScrollBar3->Position;

	writeIMX094(index,value);

}
// ---------------------------------------------------------------------------

void __fastcall TForm1::ScrollBar1Change(TObject *Sender) {
	Button21->Click();
}
// ---------------------------------------------------------------------------

void __fastcall TForm1::ScrollBar2Change(TObject *Sender) {
	Button21->Click();
}
// ---------------------------------------------------------------------------

void __fastcall TForm1::ScrollBar3Change(TObject *Sender) {
	Button26->Click();
}
// ---------------------------------------------------------------------------

void __fastcall TForm1::Button27Click(TObject *Sender) {

	unsigned short index, value;
	unsigned char data[3];

	unsigned int length = 2;
	index = hextoi(AnsiString(Edit1->Text).c_str());

	QHYCCDVendRequestWrite(g_hCam,0xb8,value,index,length,data);

	Edit2->Text = IntToHex(value, 4);

	// Button27->Caption = data[2];

}
// ---------------------------------------------------------------------------


void __fastcall TForm1::STOPSHOWClick(TObject *Sender)
{
    stop = 1;
}
// ---------------------------------------------------------------------------

void W_I2C_MICRON_Address16_OneRegister(unsigned short adr, unsigned short dat)
{
	unsigned short index, value;
	char data[2];
	unsigned int length = 2;
	index = adr;
	value = dat;
	data[0] = value / 256;
	data[1] = value % 256;

	QHYCCDVendRequestWrite(g_hCam,0xb8,value,index,length,data);
}

void __fastcall TForm1::Button2Click(TObject *Sender) {

TrackBar3->Position =10;


}
// ---------------------------------------------------------------------------

void __fastcall TForm1::Button28Click(TObject *Sender) {
	unsigned short index, value;
	unsigned char data[3];
	unsigned int length = 2;

	index = hextoi(AnsiString(Edit3->Text).c_str());
	QHYCCDVendRequestWrite(g_hCam,0xb8,value,index,length,data);
	Form1->Caption = value;
	if ((value &~0x7fff) > 0)
		Button4->Caption = "1";
	else
		Button4->Caption = "0";
	if ((value &~0xbfff) > 0)
		Button5->Caption = "1";
	else
		Button5->Caption = "0";
	if ((value &~0xdfff) > 0)
		Button6->Caption = "1";
	else
		Button6->Caption = "0";
	if ((value &~0xefff) > 0)
		Button7->Caption = "1";
	else
		Button7->Caption = "0";

	if ((value &~0xf7ff) > 0)
		Button8->Caption = "1";
	else
		Button8->Caption = "0";
	if ((value &~0xfbff) > 0)
		Button9->Caption = "1";
	else
		Button9->Caption = "0";
	if ((value &~0xfdff) > 0)
		Button10->Caption = "1";
	else
		Button10->Caption = "0";
	if ((value &~0xfeff) > 0)
		Button11->Caption = "1";
	else
		Button11->Caption = "0";

	if ((value &~0xff7f) > 0)
		Button12->Caption = "1";
	else
		Button12->Caption = "0";
	if ((value &~0xffbf) > 0)
		Button13->Caption = "1";
	else
		Button13->Caption = "0";
	if ((value &~0xffdf) > 0)
		Button14->Caption = "1";
	else
		Button14->Caption = "0";
	if ((value &~0xffef) > 0)
		Button15->Caption = "1";
	else
		Button15->Caption = "0";

	if ((value &~0xfff7) > 0)
		Button16->Caption = "1";
	else
		Button16->Caption = "0";
	if ((value &~0xfffb) > 0)
		Button17->Caption = "1";
	else
		Button17->Caption = "0";
	if ((value &~0xfffd) > 0)
		Button18->Caption = "1";
	else
		Button18->Caption = "0";
	if ((value &~0xfffe) > 0)
		Button19->Caption = "1";
	else
		Button19->Caption = "0";

}
// ---------------------------------------------------------------------------

void __fastcall TForm1::Button22Click(TObject *Sender) {
setIDLE();
setVMAX(238000);
setHMAX(2276);
setSHS(1);

DDR_Threshold(65530);

setFreqDiv(2);
EnableDDR();
EnableLock();
setLockFrames(2);
clearDDR_pulse();
setPatchPosition(15000);


Sleep(10);


releaseIDLE();



}
// ---------------------------------------------------------------------------




void setQHY5LREG_0(void){

	// [720p, 25fps input27Mhz,output50Mhz, ]
	W_I2C_MICRON_Address16_OneRegister(0x301A, 0x0001); // RESET_REGISTER
	W_I2C_MICRON_Address16_OneRegister(0x301A, 0x10D8); // RESET_REGISTER
	Sleep(100);
	/////Linear sequencer
	W_I2C_MICRON_Address16_OneRegister(0x3088, 0x8000); // SEQ_CTRL_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0025); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x5050); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2D26); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0828); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0D17); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0926); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0028); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0526); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0xA728); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0725); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x8080); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2925); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0040); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2702); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1616); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2706); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1F17); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x3626); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0xA617); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0326); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0xA417); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1F28); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0526); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2028); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0425); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2020); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2700); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x171D); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2500); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2017); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1028); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0519); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1703); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2706); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1703); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1741); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2660); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x175A); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2317); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1122); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1741); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2500); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x9027); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0026); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1828); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x002E); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2A28); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x081C); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1470); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x7003); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1470); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x7004); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1470); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x7005); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1470); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x7009); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x170C); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0014); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0020); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0014); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0050); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0314); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0020); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0314); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0050); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0414); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0020); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0414); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0050); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0514); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0020); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2405); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1400); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x5001); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2550); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x502D); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2608); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x280D); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1709); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2600); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2805); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x26A7); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2807); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2580); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x8029); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2500); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x4027); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0216); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1627); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0620); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1736); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x26A6); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1703); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x26A4); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x171F); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2805); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2620); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2804); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2520); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2027); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0017); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1D25); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0020); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1710); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2805); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1A17); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0327); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0617); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0317); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x4126); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x6017); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0xAE25); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0090); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2700); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2618); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2800); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2E2A); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2808); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1D05); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1470); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x7009); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1720); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1400); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2024); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1400); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x5002); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2550); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x502D); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2608); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x280D); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1709); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2600); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2805); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x26A7); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2807); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2580); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x8029); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2500); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x4027); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0216); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1627); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0617); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x3626); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0xA617); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0326); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0xA417); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1F28); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0526); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2028); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0425); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2020); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2700); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x171D); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2500); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2021); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1710); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2805); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1B17); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0327); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0617); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0317); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x4126); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x6017); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0xAE25); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0090); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2700); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2618); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2800); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2E2A); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2808); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1E17); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0A05); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1470); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x7009); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1616); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1616); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1616); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1616); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1616); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1616); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1616); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1616); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1616); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1616); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1616); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1616); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1616); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1616); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1616); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1616); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1400); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2024); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1400); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x502B); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x302C); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2C2C); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2C00); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0225); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x5050); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2D26); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0828); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0D17); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0926); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0028); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0526); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0xA728); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0725); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x8080); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2917); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0525); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0040); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2702); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1616); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2706); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1736); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x26A6); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1703); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x26A4); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x171F); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2805); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2620); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2804); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2520); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2027); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0017); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1E25); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0020); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2117); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1028); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x051B); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1703); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2706); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1703); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1747); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2660); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x17AE); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2500); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x9027); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0026); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1828); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x002E); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2A28); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x081E); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0831); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1440); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x4014); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2020); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1410); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1034); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1400); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1014); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0020); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1400); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x4013); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1802); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1470); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x7004); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1470); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x7003); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1470); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x7017); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2002); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1400); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2002); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1400); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x5004); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1400); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2004); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x1400); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x5022); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0314); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0020); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0314); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x0050); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2C2C); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x3086, 0x2C2C); // SEQ_DATA_PORT
	W_I2C_MICRON_Address16_OneRegister(0x309E, 0x018A); // RESERVED_MFR_309E
	W_I2C_MICRON_Address16_OneRegister(0x301A, 0x10D8); // RESET_REGISTER
	W_I2C_MICRON_Address16_OneRegister(0x3082, 0x0029); // OPERATION_MODE_CTRL
	W_I2C_MICRON_Address16_OneRegister(0x301E, 0x00C8); // DATA_PEDESTAL
	W_I2C_MICRON_Address16_OneRegister(0x3EDA, 0x0F03); // RESERVED_MFR_3EDA
	W_I2C_MICRON_Address16_OneRegister(0x3EDE, 0xC007); // RESERVED_MFR_3EDE
	W_I2C_MICRON_Address16_OneRegister(0x3ED8, 0x01EF); // RESERVED_MFR_3ED8
	W_I2C_MICRON_Address16_OneRegister(0x3EE2, 0xA46B); // RESERVED_MFR_3EE2
	W_I2C_MICRON_Address16_OneRegister(0x3EE0, 0x067D); // RESERVED_MFR_3EE0
	W_I2C_MICRON_Address16_OneRegister(0x3EDC, 0x0070); // RESERVED_MFR_3EDC
	W_I2C_MICRON_Address16_OneRegister(0x3044, 0x0404); // DARK_CONTROL
	W_I2C_MICRON_Address16_OneRegister(0x3EE6, 0x4303); // RESERVED_MFR_3EE6
	W_I2C_MICRON_Address16_OneRegister(0x3EE4, 0xD208); // DAC_LD_24_25
	W_I2C_MICRON_Address16_OneRegister(0x3ED6, 0x00BD); // RESERVED_MFR_3ED6
	W_I2C_MICRON_Address16_OneRegister(0x3EE6, 0x8303); // RESERVED_MFR_3EE6
	W_I2C_MICRON_Address16_OneRegister(0x30E4, 0x6372); // RESERVED_MFR_30E4
	W_I2C_MICRON_Address16_OneRegister(0x30E2, 0x7253); // RESERVED_MFR_30E2
	W_I2C_MICRON_Address16_OneRegister(0x30E0, 0x5470); // RESERVED_MFR_30E0
	W_I2C_MICRON_Address16_OneRegister(0x30E6, 0xC4CC); // RESERVED_MFR_30E6
	W_I2C_MICRON_Address16_OneRegister(0x30E8, 0x8050); // RESERVED_MFR_30E8
	Sleep(200);

}


double setQHY5LREG_PLL(unsigned char clock){

if(clock==0){

	W_I2C_MICRON_Address16_OneRegister(0x302A, 14); // DIV           14
	W_I2C_MICRON_Address16_OneRegister(0x302C, 1); // DIV
	W_I2C_MICRON_Address16_OneRegister(0x302E, 3); // DIV
	W_I2C_MICRON_Address16_OneRegister(0x3030, 42); //MULTI          44


	W_I2C_MICRON_Address16_OneRegister(0x3082, 0x0029); // OPERATION_MODE_CTRL

	W_I2C_MICRON_Address16_OneRegister(0x30B0, 0x1330); // DIGITAL_TEST    5370: PLL BYPASS   1370  USE PLL

	W_I2C_MICRON_Address16_OneRegister(0x305e, 0x00ff); // gain
	W_I2C_MICRON_Address16_OneRegister(0x3012, 0x0020);
	// coarse integration time

	W_I2C_MICRON_Address16_OneRegister(0x3064, 0x1802); // EMBEDDED_DATA_CTRL

	return 1.0;

}

if(clock==1){

	W_I2C_MICRON_Address16_OneRegister(0x302A, 14); // DIV           14
	W_I2C_MICRON_Address16_OneRegister(0x302C, 1); // DIV
	W_I2C_MICRON_Address16_OneRegister(0x302E, 3); // DIV
	W_I2C_MICRON_Address16_OneRegister(0x3030, 65); //MULTI          44


	W_I2C_MICRON_Address16_OneRegister(0x3082, 0x0029); // OPERATION_MODE_CTRL

	W_I2C_MICRON_Address16_OneRegister(0x30B0, 0x1330); // DIGITAL_TEST    5370: PLL BYPASS   1370  USE PLL    BIT 6 =0 -> 1330  5330
	W_I2C_MICRON_Address16_OneRegister(0x305e, 0x00ff); // gain
	W_I2C_MICRON_Address16_OneRegister(0x3012, 0x0020);
	// coarse integration time

	W_I2C_MICRON_Address16_OneRegister(0x3064, 0x1802); // EMBEDDED_DATA_CTRL

	return 65/14/3;

}

if(clock==2){

	W_I2C_MICRON_Address16_OneRegister(0x302A, 14); // DIV           14
	W_I2C_MICRON_Address16_OneRegister(0x302C, 1); // DIV
	W_I2C_MICRON_Address16_OneRegister(0x302E, 3); // DIV
	W_I2C_MICRON_Address16_OneRegister(0x3030, 57); //MULTI          44


	W_I2C_MICRON_Address16_OneRegister(0x3082, 0x0029); // OPERATION_MODE_CTRL

	W_I2C_MICRON_Address16_OneRegister(0x30B0, 0x1330); // DIGITAL_TEST    5370: PLL BYPASS   1370  USE PLL
	W_I2C_MICRON_Address16_OneRegister(0x305e, 0x00ff); // gain
	W_I2C_MICRON_Address16_OneRegister(0x3012, 0x0020);
	// coarse integration time

	W_I2C_MICRON_Address16_OneRegister(0x3064, 0x1802); // EMBEDDED_DATA_CTRL

	return 57/14/3;

}



}
















/*
void setExposureTime_QHY5LII(unsigned long i){
//��Ҫ����Ĳ���: CMOSCLK


double CMOSCLK=48;
double pixelPeriod;
pixelPeriod= 1/CMOSCLK;  //unit: us

double A,Q;
double P1,P2,P3;
double RowTime;
double ExpTime;
unsigned short REG04,REG05,REG08,REG09,REG23,REG22;
unsigned short REG23_20,REG22_54,REG23_54;
double MaxShortExpTime;


REG300C= QDbgI2CRead(g_hCam,0x300C);
REG05= QDbgI2CRead(g_hCam,0x05);
REG08= QDbgI2CRead(g_hCam,0x08);
REG09= QDbgI2CRead(g_hCam,0x09);
REG22= QDbgI2CRead(g_hCam,0x22);
REG23= QDbgI2CRead(g_hCam,0x23);




Form1->TrackBarExposure->Max =10000000;

ExpTime=i;


RowTime=REG300C*pixelPeriod;


MaxShortExpTime=15000*RowTime;








	unsigned char buf[4];




if (ExpTime>MaxShortExpTime) {
	QDbgI2CWrite(g_hCam,0x09,15000);

	//Sleep(1000);

	ExpTime=ExpTime-MaxShortExpTime;

	buf[0]=0;
	buf[1]=0;
	buf[2]=MSB(ExpTime/1000);
	buf[3]=LSB(ExpTime/1000);
	QDbgVendorReqWrite(g_hCam,0xc1,0x00,0x00,buf,4);
	ExpTime=ExpTime+MaxShortExpTime;
	REG09=15000;
}

else{
	buf[0]=0;
	buf[1]=0;
	buf[2]=0;
	buf[3]=0;
	QDbgVendorReqWrite(g_hCam,0xc1,0x00,0x00,buf,4);
	Sleep(100);
	REG09= ExpTime/RowTime;
	if (REG09<1 ) REG09=1;
	QDbgI2CWrite(g_hCam,0x09,REG09);
	ExpTime=REG09*RowTime;
}






Form1->Caption ="inputExpTime:"+AnsiString((double)i/1000)+"ms "+
				"MaxShortExpTime:"+AnsiString(MaxShortExpTime/1000)+"ms "+
				"outputExpTime:"+AnsiString((double)ExpTime/1000)+"ms "+
				"RowTime:"+AnsiString(RowTime)+"us "+
				"REG09:"+AnsiString(REG09);





}


*/


void __fastcall TForm1::TrackBar2Change(TObject *Sender)
{
writeIMX183(0x0e,TrackBar2->Position); Sleep(50);
Label6->Caption = TrackBar2->Position ;
}
//---------------------------------------------------------------------------

void __fastcall TForm1::TrackBar3Change(TObject *Sender)
{
writeIMX183(0x0E,MSB(TrackBar3->Position )); Sleep(50);
writeIMX183(0x0D,LSB(TrackBar3->Position )); Sleep(50);

Label5->Caption = TrackBar3->Position ;

}
//---------------------------------------------------------------------------

void writeGainR(int i){

writeIMX094(0x23,MSB(i ));   Sleep(50);
writeIMX094(0x22,LSB(i ));   Sleep(50);
}

void writeGainG(int i){

writeIMX094(0x25,MSB(i ));   Sleep(50);
writeIMX094(0x24,LSB(i ));   Sleep(50);
writeIMX094(0x29,MSB(i ));   Sleep(50);
writeIMX094(0x28,LSB(i ));   Sleep(50);
}

void writeGainB(int i){

writeIMX094(0x27,MSB(i ));   Sleep(50);
writeIMX094(0x26,LSB(i ));   Sleep(50);
}

void __fastcall TForm1::TrackBar4Change(TObject *Sender)
{
writeGainR(TrackBar4->Position );

				Form1->Caption =TrackBar4->Position ;
}
//---------------------------------------------------------------------------

void __fastcall TForm1::TrackBar5Change(TObject *Sender)
{
writeGainG(TrackBar5->Position);
}
//---------------------------------------------------------------------------

void __fastcall TForm1::TrackBar6Change(TObject *Sender)
{writeGainB(TrackBar6->Position);
}
//---------------------------------------------------------------------------


void __fastcall TForm1::TrackBar1Change(TObject *Sender)
{

writeIMX094(0x07,MSB(TrackBar1->Position )); Sleep(50);
writeIMX094(0x06,LSB(TrackBar1->Position )); Sleep(50);

Label4->Caption = TrackBar1->Position ;

}
//---------------------------------------------------------------------------



void __fastcall TForm1::FormCreate(TObject *Sender)
{
	QHYCCDImport();

	bitmapHist->Width = Form1->ImagePreview->Width;
	bitmapHist->Height = Form1->ImagePreview->Height;
	bitmapHist->PixelFormat = pf24bit;

	FComPort1->Active=0;



}
//---------------------------------------------------------------------------




void __fastcall TForm1::FormClose(TObject *Sender, TCloseAction &Action)
{

	StopQHYCCDLive(g_hCam);
    ex = false;
}
//---------------------------------------------------------------------------




void __fastcall TForm1::trckbr1Change(TObject *Sender)
{
writeIMX183(0x70,MSB(trckbr1->Position )); Sleep(50);
writeIMX183(0x6f,LSB(trckbr1->Position )); Sleep(50);

Label7->Caption = trckbr1->Position ;

}
//---------------------------------------------------------------------------





void __fastcall TForm1::TrackBar7Change(TObject *Sender)
{


writeIMX183(0x72,MSB(TrackBar7->Position )); Sleep(50);
writeIMX183(0x71,LSB(TrackBar7->Position )); Sleep(50);

Label8->Caption = TrackBar7->Position ;
}
//---------------------------------------------------------------------------

void __fastcall TForm1::TrackBar8Change(TObject *Sender)
{

setOFFSET(TrackBar8->Position );


LabelOffset->Caption = TrackBar8->Position ;
}
//---------------------------------------------------------------------------

void __fastcall TForm1::TrackBar9Change(TObject *Sender)
{
writeIMX183(0x1B,TrackBar9->Position ); Sleep(50);
Label10->Caption = TrackBar9->Position ;

}
//---------------------------------------------------------------------------

void __fastcall TForm1::TrackBar10Change(TObject *Sender)
{

writeIMX183(0x1d,MSB(TrackBar10->Position )); Sleep(50);
writeIMX183(0x1c,LSB(TrackBar10->Position )); Sleep(50);

Label1->Caption = TrackBar10->Position ;
}
//---------------------------------------------------------------------------

void __fastcall TForm1::TrackBar11Change(TObject *Sender)
{
writeMN34230(0x54,TrackBar11->Position ); Sleep(50);


Label2->Caption = TrackBar11->Position ;
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button32Click(TObject *Sender)
{
writeIMX183(0x00,0x01); Sleep(50);
writeIMX183(0x00,0x00); Sleep(50);
}
//---------------------------------------------------------------------------

void __fastcall TForm1::CheckBox1Click(TObject *Sender)
{


if (CheckBox1->Checked==1) writeFPGA(30,1);
else                       writeFPGA(30,0);



}
//---------------------------------------------------------------------------

void __fastcall TForm1::TrackBar12Change(TObject *Sender)
{
	writeFPGA(11,TrackBar12->Position );
	Label17->Caption ="��Ƶϵ��:"+AnsiString(TrackBar12->Position);
}
//---------------------------------------------------------------------------

void __fastcall TForm1::CheckBox2Click(TObject *Sender)
{
if (CheckBox2->Checked ==1)  writeFPGA(34,1);
else                         writeFPGA(34,0);
}
//---------------------------------------------------------------------------

void __fastcall TForm1::TrackBar13Change(TObject *Sender)
{
DDR_Threshold(TrackBar13->Position );

Label20->Caption  ="DDR������ֵ:"+AnsiString(TrackBar13->Position *2)+"K";
}
//---------------------------------------------------------------------------

void __fastcall TForm1::TrackBar14Change(TObject *Sender)
{
writeFPGA(18,TrackBar14->Position );
}
//---------------------------------------------------------------------------

void __fastcall TForm1::TrackBar15Change(TObject *Sender)
{
writeFPGA(19,TrackBar15->Position );
}
//---------------------------------------------------------------------------

void __fastcall TForm1::TrackBar16Change(TObject *Sender)
{
writeFPGA(20,TrackBar16->Position );
}
//---------------------------------------------------------------------------

void __fastcall TForm1::TrackBar17Change(TObject *Sender)
{
writeFPGA(21,TrackBar17->Position );
}
//---------------------------------------------------------------------------


void __fastcall TForm1::CheckBox3Click(TObject *Sender)
{
  if(CheckBox3->Checked ==1)  writeFPGA(35,0);
  else                        writeFPGA(35,1);

}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button25Click(TObject *Sender)
{

clearDDR_pulse();



}
//---------------------------------------------------------------------------


void __fastcall TForm1::TrackBar20Change(TObject *Sender)
{
	unsigned char data[2];
	unsigned char index;
	unsigned int length = 2;

	//index = 0x3014;

	data[0] = (TrackBar20->Position); //IMX174�ļĴ�������Ϊ8λ������ȥ����8λ

	QHYCCDVendRequestWrite(g_hCam,0xb8,0x3014,index,1,data);



	//writeMN34230(0x52,TrackBar20->Position );


 //Label15->Caption = "GAIN:"+AnsiString(TrackBar20->Position ) ;
}
//---------------------------------------------------------------------------









void __fastcall TForm1::CheckBox6Click(TObject *Sender)
{
	if (CheckBox6->Checked==1)  writeMN34230(0x002D,0);
    else                        writeMN34230(0x002D,1);
}
//---------------------------------------------------------------------------






void __fastcall TForm1::Button33Click(TObject *Sender)
{
writeIMX094(54,1);

writeIMX094(0x07,MSB(4985)); Sleep(50);
writeIMX094(0x06,LSB(4985)); Sleep(50);
writeGainR(3800 );  Sleep(50);
writeGainG(3800 );  Sleep(50);
writeGainB(3800 );  Sleep(50);
writeFPGA(18,200 );
setOFFSET(45);
setSHS(0);

Form1->Caption = TrackBar1->Position ;
}
//---------------------------------------------------------------------------









void __fastcall TForm1::Button24Click(TObject *Sender)
{
	writeFPGA(35,0);
	writeFPGA(36,0);

	writeFPGA(0x0b,2);
	writeFPGA(1,0);
	writeFPGA(30,1);

	writeFPGA(0,0);//cmos_reset
	writeFPGA(0,1);
	writeFPGA(2,1);//

	writeFPGA(22,0);//VMAX
	writeFPGA(23,0);
	writeFPGA(24,0x06);
	writeFPGA(25,0x82);

	writeFPGA(26,0);//HMAX
	writeFPGA(27,0);
	writeFPGA(28,0x08);
	writeFPGA(29,0xe4);

	  /*
	//UpdateTrackBar and Label
	TrackBar18->Position = 1666;
	Label13->Caption = "VMAX1:"+AnsiString(TrackBar18->Position );
	TrackBar19->Position = 2276;
	Label14->Caption = "HMAX1:"+AnsiString(TrackBar19->Position ) ;
		*/

	writeFPGA(45, 0x06);//max_line_valid
	writeFPGA(46, 0x74);
	writeFPGA(47, 0x03);//max_pix_valid
	writeFPGA(48, 0x9d);

	IMX094_FULL_v_1_3();

	writeFPGA(35,1);
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button35Click(TObject *Sender)
{
	writeFPGA(35,0);

	start_10bit_33bin();

	writeFPGA(35,1);
}
//---------------------------------------------------------------------------












void __fastcall TForm1::Button39Click(TObject *Sender)
{
;

}
//---------------------------------------------------------------------------





void __fastcall TForm1::Timer1Timer(TObject *Sender)
{
	unsigned char data[16];
	AnsiString str;

	if(camlink)
	{
		QHYCCDVendRequestRead(g_hCam,0xbC,0,4,3,data);

		unsigned int length = data[0] * 65536 + data[1] * 256 + data[2];

		str = "DDRBuffer Packages Num = " + AnsiString(length);

		str += "\n";

		str += "Header Num = " + AnsiString(headernum);

		str += "\n";

		str += "2nd Frame length = " + AnsiString(total_length2nd);
	}


}
//---------------------------------------------------------------------------

void SWIFT_MSBLSB(uint8_t *ImgData, uint32_t x, uint32_t y)
{
    uint32_t i = 0;
	uint8_t temp;

    while(i < x * y * 2)
    {
        temp = ImgData[i + 1];
        ImgData[i + 1] = ImgData[i];
        ImgData[i] = temp;
        i += 2;
    }
}

unsigned char rawarray[7400 * 6000 * 2];



void __fastcall TForm1::Button45Click(TObject *Sender)
{
	unsigned int ret = QHYCCD_ERROR;
	unsigned char *ImgData = new unsigned char[24208480 * 2];
	int l;
	CvScalar RMS,STD,Cons;

	unsigned int w, h, bpp,channels;
	IplImage *img = NULL;
	IplImage *cimg= NULL;
	IplImage *FlatA=NULL;
	IplImage *FlatB=NULL;
	IplImage *BiasA=NULL;
	IplImage *BiasB=NULL;
	AnsiString str="";
	ex = true;

	SetQHYCCDStreamMode(g_hCam,1);
		InitQHYCCD(g_hCam);
	SetQHYCCDBinMode(g_hCam,1,1);
	double chipx,chipy,pixelx,pixely;
	unsigned int imagex,imagey,t;
	GetQHYCCDChipInfo(g_hCam,&chipx,&chipy,&imagex,&imagey,&pixelx,&pixely,&t);
	//printf("**********************imagex = %d imagey = %d\n\n\n\n\n",imagex,imagey);


	imagex=1000;
	imagey=1000;
	SetQHYCCDResolution(g_hCam,1000,1000,imagex,imagey);
	SetQHYCCDParam(g_hCam,CONTROL_DDR,0);
	SetQHYCCDParam(g_hCam,CONTROL_OFFSET,30);
	SetQHYCCDParam(g_hCam,CONTROL_USBTRAFFIC,0);
	SetQHYCCDParam(g_hCam,CONTROL_TRANSFERBIT,16);
	SetQHYCCDParam(g_hCam,CONTROL_EXPOSURE,CurrentExpTime);
	SetQHYCCDParam(g_hCam,CONTROL_GAIN,0);  //��ǰ��ֵΪ20�����޸�Ϊ0_YRX
	SetQHYCCDParam(g_hCam,CONTROL_WBR,64);
	SetQHYCCDParam(g_hCam,CONTROL_WBG,64);
	SetQHYCCDParam(g_hCam,CONTROL_WBB,64);
	SetQHYCCDParam(g_hCam,CONTROL_COOLER,-10);

	BeginQHYCCDLive(g_hCam);

	char temp[64];

	while(ex)
	{
		ret=GetQHYCCDLiveFrame(g_hCam,&w, &h, &bpp, &channels,ImgData);
		Form1->Caption = AnsiString(step)+" " + AnsiString(w)+ " "+ AnsiString(h)+" "+ AnsiString(channels);
		memcpy(imageHead,ImgData,64);

		if(isGainChanged==1)
		{
		   ret=SetQHYCCDParam(g_hCam,CONTROL_GAIN,gain);
		   isGainChanged=0;
		}

	//	if(isExpChanged==1){
	//		ret=SetQHYCCDParam(g_hCam,CONTROL_EXPOSURE,CurrentExpTime);
	//		Form1->Caption =CurrentExpTime;
	//		isExpChanged=0;
	//	}


	//	cvShowImage("TestLive",img);


		if (ret == QHYCCD_SUCCESS)
		{
			if (img == NULL)
			{
				//NamedWindow("TestLive",1);
				//NamedWindow("FlatA",1);
				//NamedWindow("FlatB",1);
				//NamedWindow("BiasA",1);
				//NamedWindow("BiasB",1);

				img = cvCreateImageHeader(cvSize(w, h), bpp, 1);

				FlatA = cvCreateImage(cvSize(w, h), bpp, 1);
				FlatB = cvCreateImage(cvSize(w, h), bpp, 1);
				BiasA = cvCreateImage(cvSize(w, h), bpp, 1);
				BiasB = cvCreateImage(cvSize(w, h), bpp, 1);

				cimg = cvCreateImage(cvSize(w, h), bpp, 3);
				img->imageData = (char *)ImgData;

				FComPort1->Open() ;
			}

		}

			str="";

			if(img!=NULL && ret==QHYCCD_SUCCESS)
			{	//showCVToTImage(img,Image1);

				showHistOnWindows16(img,Form1->ImagePreview);
				step++;
				//Sleep(50);
				if(isTest==1)
				{
					if(step==1)  FComPort1->WriteChar('F');

					if(step==10) { cvCopy(img,FlatA,NULL); showHistOnWindows16(FlatA,Form1->ImageFlat1);}
					if(step==20) { cvCopy(img,FlatB,NULL); showHistOnWindows16(FlatA,Form1->ImageFlat2);}
					if(step==30) FComPort1->WriteChar('N') ;

					if(step==40) { cvCopy(img,BiasA,NULL); showHistOnWindows16(BiasA,Form1->ImageBias1);}
					if(step==50) { cvCopy(img,BiasB,NULL); showHistOnWindows16(BiasA,Form1->ImageBias2);}
					if(step==60)
					{
						FComPort1->WriteChar('F') ;
						Cons.val[0]=15000;
						cvAddS(FlatA,Cons,FlatA,NULL);
						cvSub(FlatA,FlatB,FlatA,NULL);
						cvSetImageROI(FlatA,cvRect(400,400,500,500));
						cvAvgSdv(FlatA, &RMS, &STD, NULL);
						cvResetImageROI(FlatA);

						double STDF12=STD.val[0];

						cvSetImageROI(FlatB,cvRect(400,400,500,500));
						cvAvgSdv(FlatB, &RMS, &STD, NULL);
						cvResetImageROI(FlatB);

						double RMSF2=RMS.val[0];

						if(RMSF2>25000)
						{
							step = 0;

							Memo1->Text=Memo1->Text+"\r\n";
							Memo1->Text=Memo1->Text+"Current Gain = "+AnsiString(gain)+"\r\n";
							Memo1->Text=Memo1->Text+"RMS = "+ AnsiString(RMSF2) + ", and it is too high.";
							Memo1->Text=Memo1->Text+"\r\n"+ "Try finding suitable exposure time"+"\r\n";

							CurrentExpTime=CurrentExpTime*24000/RMSF2;
							SetQHYCCDParam(g_hCam,CONTROL_EXPOSURE,CurrentExpTime);
							Memo1->Text=Memo1->Text+"Exposure time decreased."+"\r\n"+"Current exposure time = "+AnsiString(CurrentExpTime)+"\r\n";

							continue;
						}

						cvSetImageROI(BiasA,cvRect(400,400,500,500));
						cvAvgSdv(BiasA, &RMS, &STD, NULL);
						cvResetImageROI(BiasA);






						double RMSB1=RMS.val[0];
						double STDB1=STD.val[0];

						Memo1->Text=Memo1->Text+"\r\n";
						Memo1->Text=Memo1->Text+"Current Gain="+AnsiString(gain)+"\r\n";
						Memo1->Text=Memo1->Text+"STD(F1-F2)="+AnsiString(STDF12)+"\r\n";
						Memo1->Text=Memo1->Text+"RMS(F2)="+AnsiString(RMSF2)+"\r\n";
						Memo1->Text=Memo1->Text+"STD(B1)="+AnsiString(STDB1)+"\r\n";
						Memo1->Text=Memo1->Text+"RMS(B1)="+AnsiString(RMSB1)+"\r\n";




						//calculate the BIAS(A-B)

						Cons.val[0]=15000;
						cvAddS(BiasA,Cons,BiasA,NULL);
						cvSub(BiasA,BiasB,BiasA,NULL);
						//now biasA is Bias(A-B)
						cvSetImageROI(BiasA,cvRect(400,400,500,500));
						cvAvgSdv(BiasA, &RMS, &STD, NULL);
						cvResetImageROI(BiasA);


						double STDB12=STD.val[0];
						Memo1->Text=Memo1->Text+"STD(B1-B2)="+AnsiString(STDB12)+"\r\n";

						if(STDF12==0)
						{
							Memo1->Text=Memo1->Text+"Test failed this time"+"\r\n";
						}
						else
						{
							double systemGain;



							MemoGain->Text=MemoGain->Text+AnsiString(gain)+"\r\n";

							systemGain = (RMSF2 - RMSB1) / (STDF12 * STDF12 / 2);
							Memo1->Text=Memo1->Text+"systemGain="+AnsiString(systemGain)+"\r\n";
							MemoSystemGain->Text=MemoSystemGain->Text+AnsiString(systemGain)+"\r\n";

							double readoutNoise,readoutNoise12;
							readoutNoise =   systemGain * STDB1;
							readoutNoise12 = systemGain * STDB12 / 1.414;
							Memo1->Text=Memo1->Text+"readoutNoise="+AnsiString(readoutNoise)+"\r\n";
							Memo1->Text=Memo1->Text+"readoutNoise12="+AnsiString(readoutNoise12)+"\r\n";
							MemoReadoutNoise->Text=MemoReadoutNoise->Text+AnsiString(readoutNoise)+"\r\n";
							MemoReadoutNoise2->Text=MemoReadoutNoise2->Text+AnsiString(readoutNoise12)+"\r\n";
							double fullwell;
							fullwell=systemGain * 65535;
							Memo1->Text=Memo1->Text+"fullwell="+AnsiString(fullwell)+"\r\n";
							MemoFullWell->Text=MemoFullWell->Text+AnsiString(fullwell)+"\r\n";
							 /*
							if (gain < 10) 	gain+=2;
							else 			gain+=5;
								 */
								 gain=gain+gainStep;

							isGainChanged = 1;
						}
					}
					if(step==100) step = 0;
				}

			  if(isFindExposure==1)
			  {
				if(step==5)
					{
						FComPort1->WriteChar('F');
						//Memo1->Text=Memo1->Text+"currentRMS:"+AnsiString(CurrentFlatRMS)+"\r\n";

						if(CurrentFlatRMS>60000)
						{
							CurrentExpTime=CurrentExpTime/2;
							SetQHYCCDParam(g_hCam,CONTROL_EXPOSURE,CurrentExpTime);
							Memo1->Text=Memo1->Text+"decrease  Exp:"+AnsiString(CurrentExpTime)+"\r\n";
						}

						else if(CurrentFlatRMS>30000)
						{
							CurrentExpTime=CurrentExpTime*30000/CurrentFlatRMS;
							SetQHYCCDParam(g_hCam,CONTROL_EXPOSURE,CurrentExpTime);
							Memo1->Text=Memo1->Text+"decrease  Exp:"+AnsiString(CurrentExpTime)+"\r\n";
						}

						if (CurrentFlatRMS!=0)
						{
							if (CurrentFlatRMS<25000)
							{
								CurrentExpTime=CurrentExpTime*30000/CurrentFlatRMS;
								SetQHYCCDParam(g_hCam,CONTROL_EXPOSURE,CurrentExpTime);
								Memo1->Text=Memo1->Text+"increase  Exp:"+AnsiString(CurrentExpTime)+"\r\n";
							}
						}
						Memo1->Text=Memo1->Text+"\r\n";
					}

				if(step==25)
				{
					cvCopy(img,FlatA,NULL);

				   cvSetImageROI(FlatA,cvRect(400,400,500,500));
				   cvAvgSdv(FlatA, &RMS, &STD, NULL);
				   cvResetImageROI(FlatA);

				   double STDF1=STD.val[0];
				   double RMSF1=RMS.val[0];
				   CurrentFlatRMS=RMSF1;

				   Memo1->Text=Memo1->Text+"CurrentRMS"+AnsiString(CurrentFlatRMS)+"\r\n";

				   FComPort1->WriteChar('N');
				   isFindExposure=0;
				   step =0 ;
				   isTest = 1;
				}

			}

			//ShowImage("TestLive",img);
			//ShowImage("FlatA",FlatA);
			//ShowImage("FlatB",FlatB);
			//cvShowImage("BiasA",BiasA);
			//cvShowImage("BiasB",BiasB);

			// showCVToTImage(cimg,Image1);
		}
		Application->ProcessMessages();
	}


	//cvDestroyWindow("TestLive");
	//cvDestroyWindow("FlatA");
	//cvDestroyWindow("FlatB");
	//cvDestroyWindow("BiasA");
	//cvDestroyWindow("BiasB");

	cvReleaseImage(&img);
	cvReleaseImage(&cimg);
	cvReleaseImage(&FlatA);
	cvReleaseImage(&FlatB);
	cvReleaseImage(&BiasA);
	cvReleaseImage(&BiasB);

	delete(ImgData);
	FComPort1->Close() ;
}
//---------------------------------------------------------------------------

void __fastcall TForm1::ButtonTestClick(TObject *Sender)
{
	step=0;
	isTest=1;

	Memo1->Clear();
	MemoGain->Clear();
	MemoSystemGain->Clear();
	MemoFullWell->Clear();
	MemoReadoutNoise->Clear();
  	MemoReadoutNoise2->Clear();

	MemoGain->Text="Gain\r\n"   ;
	MemoSystemGain->Text="SystemGain\r\n";
	MemoFullWell->Text="FullWell\r\n";
	MemoReadoutNoise->Text="ReadoutNoise\r\n";

	gainStep=Edit6->Text.ToInt();
	gainStart=Edit7->Text.ToInt();
	gain=gainStart;
   	isGainChanged=1;


}
//---------------------------------------------------------------------------

void __fastcall TForm1::ButtonAETAClick(TObject *Sender)
{
	step=0;
	isFindExposure=1;
	isTest=0;
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button46Click(TObject *Sender)
{
	 FComPort1->Open() ;
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button47Click(TObject *Sender)
{
	 FComPort1->WriteChar('N');

}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button48Click(TObject *Sender)
{
     FComPort1->WriteChar('F');
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button49Click(TObject *Sender)
{
	Memo1->Text="";
}
//---------------------------------------------------------------------------

void __fastcall TForm1::ButtonGainClick(TObject *Sender)
{
	isGainBarVisible=!	 isGainBarVisible;
	if(isGainBarVisible==1) { ScrollBar5->Visible =1;  ScrollBar6->Visible =1;ScrollBar7->Visible =1;ScrollBar8->Visible =1;}
	else                    { ScrollBar5->Visible =0;  ScrollBar6->Visible =0;ScrollBar7->Visible =0;ScrollBar8->Visible =0;}
}
//---------------------------------------------------------------------------

void __fastcall TForm1::ScrollBar5Change(TObject *Sender)
{
	gain=ScrollBar5->Position  ;
	ButtonGain->Caption="Gain:"+AnsiString(gain);
	isGainChanged=1;
}
//---------------------------------------------------------------------------

void __fastcall TForm1::ScrollBar6Change(TObject *Sender)
{
	gainR=ScrollBar6->Position  ;
	isColorGainChanged=1;
}
//---------------------------------------------------------------------------

void __fastcall TForm1::ScrollBar7Change(TObject *Sender)
{
	gainG=ScrollBar7->Position  ;
	isColorGainChanged=1;
}
//---------------------------------------------------------------------------

void __fastcall TForm1::ScrollBar8Change(TObject *Sender)
{
	gainB=ScrollBar8->Position  ;
	isColorGainChanged=1;
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button50Click(TObject *Sender)
{
	gain=0;
	isGainChanged=1;
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button51Click(TObject *Sender)
{
	gain=25;
	isGainChanged=1;
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button52Click(TObject *Sender)
{
	gain=50;
	isGainChanged=1;
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button53Click(TObject *Sender)
{
	gain=75;
	isGainChanged=1;
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button54Click(TObject *Sender)
{
	gain=100;
	isGainChanged=1;
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button70Click(TObject *Sender)
{
	step=0;
	isTest=0;
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Memo1Change(TObject *Sender)
{
	SendMessage(Memo1->Handle,WM_VSCROLL,SB_BOTTOM,0);
}
//---------------------------------------------------------------------------


void __fastcall TForm1::ComboBoxComPortChange(TObject *Sender)
{
FComPort1->DeviceName=ComboBoxComPort->Text;
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button30Click(TObject *Sender)
{
FComPort1->Active = !FComPort1->Active;


if (FComPort1->Active ==1)   Button30->Caption = "ComPort Actived";
else                         Button30->Caption = "ComPort Not Active";








}
//---------------------------------------------------------------------------


void __fastcall TForm1::ButtonConnectAndModeClick(TObject *Sender)
{

//This button will connect the camera for first and get the readMode and display it. Then close the camera
//After click this button  , user need to select fromt the drop list box to select a read mode
//then connect the Connect Button
	 char camid[64];
	 int n;
	 AnsiString CameraName,ColorType, ProduceDate, FactoryLocation;

	 unsigned int  TotalReadModes;

	 if(g_hCam == NULL)
	 {
		 InitQHYCCDResource();
		 int num = ScanQHYCCD();
		 if(num > 0)
		 {
			 GetQHYCCDId(0,camid);
			 g_hCam = OpenQHYCCD(camid);
			 camlink = true;
			 ButtonConnectAndMode->Caption = AnsiString(camid);;


			 GetQHYCCDNumberOfReadModes(g_hCam, &TotalReadModes);
			 LabelTotalReadMode->Caption = "Total ReadModes:"+AnsiString(TotalReadModes);
			 Memo1->Text=Memo1->Text+LabelTotalReadMode->Caption+"\r\n";


			 Memo1->Text=Memo1->Text+"Search All Readout Mode\r\n";
			 char readModeName[64];
			 for (int i = 0; i < TotalReadModes; i++) {
				 GetQHYCCDReadModeName(g_hCam,i,readModeName);
				 ComboBoxReadMode->Items->Add(readModeName);
				 Memo1->Text=Memo1->Text+readModeName+"\r\n";
			 }

			 ComboBoxReadMode->ItemIndex=0;
			 Memo1->Text=Memo1->Text+"default selected:#0\r\n";
			 Memo1->Text=Memo1->Text+"Now Please select from list\r\n";


			 CloseQHYCCD(g_hCam);
			 g_hCam=NULL;
			 ReleaseQHYCCDResource();
		 }

	 }
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button29Click(TObject *Sender)
{

//This button will connect the camera for first and get the readMode and display it. Then close the camera
//After click this button  , user need to select fromt the drop list box to select a read mode
//then connect the Connect Button
	 char camid[64];
	 int n;


	 if(g_hCam != NULL)
	 {
			 CloseQHYCCD(g_hCam);
			 g_hCam=NULL;
			 ReleaseQHYCCDResource();
			 Button1->Caption ="Disconnected";
			 Memo1->Text=Memo1->Text+"Camera Disconnected\r\n";
	 }


}
//---------------------------------------------------------------------------

void __fastcall TForm1::Edit6Change(TObject *Sender)
{
gainStep=Edit6->Text.ToInt();
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Edit7Change(TObject *Sender)
{
gainStart=Edit7->Text.ToInt();
gain=gainStart;
isGainChanged=1;
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button55Click(TObject *Sender)
{
CurrentExpTime=1000;
	SetQHYCCDParam(g_hCam,CONTROL_EXPOSURE,CurrentExpTime);

}
//---------------------------------------------------------------------------


void __fastcall TForm1::Button56Click(TObject *Sender)
{
CurrentExpTime=2*1000;
	SetQHYCCDParam(g_hCam,CONTROL_EXPOSURE,CurrentExpTime);
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button57Click(TObject *Sender)
{
CurrentExpTime=5*1000;
	SetQHYCCDParam(g_hCam,CONTROL_EXPOSURE,CurrentExpTime);
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button58Click(TObject *Sender)
{
CurrentExpTime=7*1000;
	SetQHYCCDParam(g_hCam,CONTROL_EXPOSURE,CurrentExpTime);
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button59Click(TObject *Sender)
{
CurrentExpTime=10*1000;
	SetQHYCCDParam(g_hCam,CONTROL_EXPOSURE,CurrentExpTime);
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button60Click(TObject *Sender)
{
CurrentExpTime=15*1000;
	SetQHYCCDParam(g_hCam,CONTROL_EXPOSURE,CurrentExpTime);
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button61Click(TObject *Sender)
{
CurrentExpTime=20*1000;
	SetQHYCCDParam(g_hCam,CONTROL_EXPOSURE,CurrentExpTime);
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button62Click(TObject *Sender)
{
CurrentExpTime=25*1000;
	SetQHYCCDParam(g_hCam,CONTROL_EXPOSURE,CurrentExpTime);
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button63Click(TObject *Sender)
{
CurrentExpTime=30*1000;
	SetQHYCCDParam(g_hCam,CONTROL_EXPOSURE,CurrentExpTime);
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button64Click(TObject *Sender)
{
CurrentExpTime=40*1000;
	SetQHYCCDParam(g_hCam,CONTROL_EXPOSURE,CurrentExpTime);
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button65Click(TObject *Sender)
{
CurrentExpTime=50*1000;
	SetQHYCCDParam(g_hCam,CONTROL_EXPOSURE,CurrentExpTime);
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button66Click(TObject *Sender)
{
CurrentExpTime=100*1000;
	SetQHYCCDParam(g_hCam,CONTROL_EXPOSURE,CurrentExpTime);
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button67Click(TObject *Sender)
{
CurrentExpTime=200*1000;
	SetQHYCCDParam(g_hCam,CONTROL_EXPOSURE,CurrentExpTime);
}
//---------------------------------------------------------------------------

void __fastcall TForm1::Button68Click(TObject *Sender)
{
CurrentExpTime=500*1000;
	SetQHYCCDParam(g_hCam,CONTROL_EXPOSURE,CurrentExpTime);
}
//---------------------------------------------------------------------------

